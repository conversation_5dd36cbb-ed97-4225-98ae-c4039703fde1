<template>
  <div class="home">
    <!-- 遍历路由分组 -->
    <div
      v-for="(groupRoutes, groupName) in groupedAvailableRoutes"
      :key="groupName"
      class="route-group"
    >
      <h2>{{ groupName }}</h2>
      <!-- 显示分组名称 (例如 'views', 'element', 'device') -->
      <ul>
        <!-- 遍历分组内的路由 -->
        <li v-for="route in groupRoutes" :key="route.path">
          <!-- 生成普通的 a 标签，在新窗口打开 -->
          <a :href="router.resolve(route.path).href" target="_blank" rel="noopener noreferrer">{{
            route.meta?.title || route.name || route.path
          }}</a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";

// 获取 Vue Router 实例
const router = useRouter();

// 计算属性：将路由按其路径的第一部分（通常是父目录名）进行分组
const groupedAvailableRoutes = computed(() => {
  // 获取所有已注册的路由配置
  const routes = router.options.routes;
  const groups = {}; // 用于存储分组结果的对象

  // 遍历所有路由
  routes.forEach((route) => {
    // 跳过根路径 '/' (Home 路由)
    if (route.path === "/") return;

    // 从路由路径中提取第一级目录名作为分组依据
    // 例如：从 '/element/demo1' 提取 'element'
    const pathSegments = route.path.split("/").filter(Boolean); // 分割路径并移除空字符串
    if (pathSegments.length >= 1) {
      const groupName = pathSegments[0]; // 获取第一部分作为分组名
      // 如果该分组尚不存在，则初始化为空数组
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      // 将当前路由添加到对应的分组中
      groups[groupName].push(route);
    }
    // 此处可以添加逻辑处理不符合 '/分组名/组件名' 模式的路由
  });

  // （可选）按分组名称字母顺序排序
  const sortedGroupNames = Object.keys(groups).sort();
  const sortedGroups = {};
  sortedGroupNames.forEach((name) => {
    sortedGroups[name] = groups[name];
  });

  // 返回排序后的分组对象
  return sortedGroups;
});
</script>

<style scoped>
/* --- 组件容器基本样式 --- */
.home {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1rem; /* 显式设置字体大小，与 :root 一致 */
  padding: 30px;
  line-height: 1.6;
  color: #333;
  max-width: 600px;
}

/* --- 分组标题样式 --- */
.route-group h2 {
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.6em;
  font-size: 1.2em;
  color: #1a1a1a;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.3em;
}
/* 移除第一个分组标题的上边距 */
.home > .route-group:first-of-type h2 {
  margin-top: 0;
}

/* --- 列表样式 --- */
.route-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
  padding-left: 10px; /* 轻微缩进组件链接 */
  margin-top: 0.5em;
}

/* 列表项样式 */
.route-group li {
  margin-bottom: 0.6em;
}

/* --- 链接样式 --- */
.route-group a {
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}
.route-group a:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* --- 其他通用标题/段落样式 (如果需要) --- */
.home h1 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 600;
  font-size: 1.8em;
}

.home p {
  margin-top: 0;
  margin-bottom: 1.5em;
  color: #555;
}
</style>
