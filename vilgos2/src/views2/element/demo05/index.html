<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>机组状态管理</title>
  <!-- 导入样式 -->
  <!-- 导入 ICON -->
  <link rel="icon" href="data:;base64,iVBORw0KGgo=">
  <!-- 导入 ElementPlus -->
  <link rel="stylesheet" href="/public/element-plus/index.css">
  <!-- 导入 Layui -->
  <link rel="stylesheet" href="/public/layui/css/layui.css">
  <!-- 导入 自定义CSS -->
  <link rel="stylesheet" href="index.css" />
  <!-- 其他头部信息 -->
  <style>
    .el-table__row {
      user-select: none;
    }

    .centered-text {
      display: grid;
      text-align: center;
      font-size: 24px;
    }

    .el-form--inline .el-form-item {
      margin-right: 5px !important;
    }

    .el-form-item {
      margin-bottom: 20px !important;
    }
  </style>
</head>

<body>
  <div id="app">
    <el-container>
      <el-main v-loading.lock="loading">
        <el-form ref="searchFormRef" :model="searchForm" :rules="rules" :inline="true">
          <el-form-item label="日期" prop="vDay">
            <el-date-picker v-model="searchForm.vDay" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="fun_search(searchFormRef)"
              style="width: 230px !important;"></el-date-picker>
          </el-form-item>
          <el-form-item label="名称" prop="mc">
            <el-input v-model="searchForm.mc" clearable @keyup.enter="fun_search(searchFormRef)"
              style="width: 130px;" />
          </el-form-item>
          <el-form-item label="地区" prop="dq">
            <el-select v-model="searchForm.dq" placeholder="请选择地区" multiple clearable style="width: 130px;">
              <el-option v-for="item in attrCnts.dq" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="state">
            <el-select v-model="searchForm.state" placeholder="请选择状态" multiple clearable style="width: 130px;">
              <el-option v-for="item in attrCnts.state" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fun_search(searchFormRef)">查询</el-button>
          </el-form-item>
        </el-form>

        <div class="lengendArr">
          <p>图例</p>
          <div><span class="YXStatusIcon"></span><span>并网</span></div>
          <div><span class="JXStatusIcon"></span><span>备用</span></div>
          <div><span class="LBYStatusIcon"></span><span>备用不可调</span></div>
          <div><span class="RBYStatusIcon"></span><span>紧停</span></div>
          <div><span class="RBYStatusIcon1"></span><span>临修</span></div>
        </div>

        <div style="overflow: auto; width: 100%; height: 580px;" id="dataDiv">
          <el-table :data="dataList" :height="dataTableHeight" @row-dblclick="fun_editor" border style="width: 100%">
            <el-table-column prop="dq" label="地区" width="68px" fixed="left"></el-table-column>
            <el-table-column prop="dcjz" label="机组名称" width="100px" fixed="left"></el-table-column>
            <el-table-column v-for="(item,index) in dateLength" :key="index" :label="item" width="57px"
              class-name="colorL" label-class-name="colorTitle">
              <template #default="scope">
                <span class="colorBg" v-if="scope.row.state[index] == '并网'" style="background: #96cba3"></span>
                <span class="colorBg" v-if="scope.row.state[index] == '备用'" style="background: #92D050"></span>
                <span class="colorBg" v-if="scope.row.state[index] == '备用不可调'" style="background: #00B0F0"></span>
                <span class="colorBg" v-if="scope.row.state[index] == '紧停'" style="background: #FF0000"></span>
                <span class="colorBg" v-if="scope.row.state[index] == '临修'" style="background: #FF9999"></span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <el-dialog v-model="dataFormVisible" :title="dataFormTitle" width="30%">
          <el-form ref="dataFormRef" :rules="dataFormRules" :model="dataForm" label-position="right"
            label-width="120px">
            <el-form-item label="名称：" prop="dcjz">
              <el-input v-model="dataForm.dcjz" disabled />
            </el-form-item>
            <el-form-item label="当前状态：" prop="state">
              <el-input v-model="dataForm.state" disabled />
            </el-form-item>
            <el-form-item label="变更后状态：" prop="code">
              <el-select v-model="dataForm.code" placeholder="请选择状态" clearable>
                <el-option v-for="item in attrCnts.state" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期：" prop="vDay">
              <el-date-picker v-model="dataForm.vDay" type="datetimerange" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
            </el-form-item>
          </el-form>
          <template #footer v-if="dataFormStatus != 'detail' && !loading">
            <span class="dialog-footer">
              <el-button @click="cancle(dataFormRef)">取消</el-button>
              <el-button type="primary" @click="fun_save(dataFormRef)">提交</el-button>
            </span>
          </template>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</body>
<!-- 导入 Vue3 -->
<script src="/public/vue/dist/vue.global.prod.js"></script>
<script src="/public/axios/axios.min.js"></script>
<!-- 导入 ElementPlus -->
<script src="/public/element-plus/index.full.min.js"></script>
<script src="/public/element-plus/locale/zh-cn.min.js"></script>
<!-- 导入 Layui -->
<script src="/public/layui/layui.js"></script>
<!-- 导入 Dayjs -->
<script src="/public/dayjs/dayjs.min.js"></script>
<!-- 导入 Echarts -->
<script src="/public/echarts/echarts.js"></script>
<!-- 导入 Jquery -->
<script src="/public/jquery/jquery.min.js"></script>
<!-- 导入 index.js -->
<script src="index.js" type="module"></script>

</html>