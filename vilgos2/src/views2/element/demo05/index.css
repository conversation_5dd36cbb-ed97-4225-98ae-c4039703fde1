* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

@font-face {
  font-family: 'ysbt';
  /*src: url('../../static/fonts/YouSheBiaoTiHei-2.ttf');*/
}

/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #b2ddd6;
}

/*定义滚动条轨道
内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px #b2ddd6;
  border-radius: 10px;
  background-color: #E9E7E7;
}

/*定义滑块
 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgb(7, 153, 153);
  background-color: #b2ddd6;
}


.app {
  height: 100vh;
}

.header {
  width: 100%;
  height: 80px;
  ;
  background: #05998D;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  margin: 0 40px;
}

.headerTime {
  display: flex;
  color: #fff;
  align-items: center;
}

.headerTime img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.logo_title h1 {
  font-size: 20px;
  font-weight: 600;
}

.logo_title h4 {
  font-size: 12px;
  font-weight: 600;
}

.logo_title h4 span {
  font-size: 16px;
  font-weight: 600;
}

.headerTime h5 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.line {
  margin: 0 20px;
  background: #fff;
  height: 40px;
  width: 2px;
}

.haderUser {
  color: #fff;

}

.content {
  width: 100%;
  height: calc(100% - 80px);
  background: #eff3f2;
}

.content_top {
  width: calc(100% - 40px);
  background: #fff;
  height: 100px;
  margin: 0 20px;
}

.content_top_time {
  height: 50px;
  display: flex;
  align-items: center;
}

.content_top_type {
  height: 50px;
  display: flex;
  align-items: center;
}

.content_bottom {
  width: calc(100% - 40px);
  background: #fff;
  margin: 20px;
  padding: 20px;
}

.content_bottom_btn {}

.table {
  border: 1px solid #8fbcb5;
}

.table tr td {
  border: 1px solid #d5d5d5;
  height: 36px;
  text-align: center;
}

.table tr:hover td {
  /*background: #b2ddd6;*/
  /*color:#1c9278;*/
  /*font-weight: bolder;*/
}

.table tr th {
  border: 1px solid #8fbcb5;
  height: 36px;
}

.table {
  margin-top: 20px;
}

#therd th {
  background: #b2ddd6;
  color: #1c9278;
  font-weight: bolder;
  /*width: 90px;*/
}

body {
  overflow: hidden;
}

.noData {
  display: none;
}

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 1;

}

.fixed-column {
  position: sticky;
  left: 0;
  z-index: 1;
}

.lengendArr {
  height: 40px;
  display: flex;
  padding: 5px 10px;
  position: absolute;
  right: 50px;
  top: 12px;
}

.lengendArr p {
  font-size: 14px;
  color: #666;
  line-height: 30px;
  margin-right: 10px
}

.lengendArr div {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.YXStatusIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #96cba3;
  margin: 0 5px 0 10px;
}

.JXStatusIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #92D050;
  margin: 0 5px 0 10px;
}

.LBYStatusIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00B0F0;
  margin: 0 5px 0 10px;
}

.RBYStatusIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #FF0000;
  margin: 0 5px 0 10px;
}

.RBYStatusIcon1 {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #FF9999;
  margin: 0 5px 0 10px;
}

.colorBg {
  height: 40px;
  display: block;
  width: 100px;
  padding: 8px 0;
}

.colorL .cell {
  padding: 0 !important;
}

.colorL {
  padding: 0 !important;
}

.colorTitle {
  font-size: 15px;
  padding: 8px 8px !important;
}