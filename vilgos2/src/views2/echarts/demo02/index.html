<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>element示例</title>
  <!-- 导入 ICON -->
  <link rel="icon" href="data:;base64,iVBORw0KGgo=">
  <!-- 导入 Global -->
  <link rel="stylesheet" href="/public/style.css">
  <!-- 导入 ElementPlus -->
  <link rel="stylesheet" href="/public/element-plus/index.css">
  <!-- 导入 Layui -->
  <link rel="stylesheet" href="/public/layui/css/layui.css">
  <!-- 其他头部信息 -->
  <style>
  </style>
</head>

<body>
  <div id="app" v-loading.lock="loading" class="root-container">
    <!-- 布局 1: 筛选区域 -->
    <div class="layout-search">
      <el-form ref="searchFormRef" :model="searchForm" :rules="searchFormRules" :inline="true">
        <!-- 条件容器 -->
        <div class="search-filters">
          <el-form-item label="产线" prop="plNoKey">
            <el-tree-select v-model="searchForm.plNoKey" :props="treeProps" :data="treeData" check-strictly
              :render-after-expand="false" placeholder="默认全部产线" class="search-tree-select" value-key="key" multiple
              clearable />
          </el-form-item>
          <el-form-item label="设备号" prop="equipmentNo">
            <el-input v-model="searchForm.equipmentNo" placeholder="请输入设备号" clearable class="search-input"></el-input>
          </el-form-item>
          <el-form-item label="设备故障模式" prop="stopReasonName">
            <el-select v-model="searchForm.stopReasonName" placeholder="请选择故障模式" multiple clearable
              class="search-select">
              <el-option v-for="item in attrCnts.faultModeOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围" prop="vDay">
            <el-date-picker v-model="searchForm.vDay" type="daterange" start-placeholder="开始日期" range-separator="至"
              end-placeholder="结束日期" format="YYYY/MM/DD" value-format="YYYY/MM/DD" class="search-datepicker">
            </el-date-picker>
          </el-form-item>
          <div class="search-buttons">
            <el-form-item>
              <el-button type="primary" @click="fun_search(searchFormRef)">查询</el-button>
              <el-button @click="fun_reset(searchFormRef)">重置</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <!-- 布局 2: 内容区域 -->
    <div class="layout-content">
      <div class="content-area">
        <el-row :gutter="24">
          <!-- 左侧列表 -->
          <el-col :span="6">
            <el-card shadow="never" class="list-card">
              <template #header>
                <div>
                  <span>故障模式列表</span>
                </div>
              </template>
              <el-table :data="dataList" stripe>
                <!-- 使用 v-for 动态渲染列 -->
                <el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop"
                  :label="column.label" :width="column.width" :min-width="column.minWidth" :align="column.align"
                  :show-overflow-tooltip="column.showOverflowTooltip">
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <!-- 右侧图表 -->
          <el-col :span="18">
            <el-card shadow="never" class="chart-card">
              <template #header>
                <div>
                  <span>故障模式分布图（%）</span>
                </div>
              </template>
              <div ref="chartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 布局 3: 分析功能区域 -->
    <div class="layout-analysis">
      <el-card class="analysis-card">
        <template #header>
          <div>
            <span>分析功能</span>
          </div>
        </template>
        <div>
          <el-input type="textarea" v-model="analysisText" :rows="3" readonly resize="none" />
        </div>
      </el-card>
    </div>
  </div>
</body>
<!-- 导入 Vue3 -->
<script src="/public/vue/dist/vue.global.prod.js"></script>
<script src="/public/axios/axios.min.js"></script>
<!-- 导入 ElementPlus -->
<script src="/public/element-plus/index.full.min.js"></script>
<script src="/public/element-plus/locale/zh-cn.min.js"></script>
<!-- 导入 Layui -->
<script src="/public/layui/layui.js"></script>
<!-- 导入 Dayjs -->
<script src="/public/dayjs/dayjs.min.js"></script>
<!-- 导入 Echarts -->
<script src="/public/echarts/echarts.js"></script>
<!-- 导入 Jquery -->
<script src="/public/jquery/jquery.min.js"></script>
<!-- 导入 index.js -->
<script src="./js/index.js" type="module"></script>
<!-- 导入 index.css (使用标准 link 标签) -->
<link rel="stylesheet" href="./css/index.css">