<template>
  <div v-loading.lock="loading" class="root-container">
    <!-- 布局 1: 筛选区域 -->
    <div class="layout-search">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        :rules="searchFormRules"
        :inline="true"
      >
        <!-- 条件容器 -->
        <div class="search-filters">
          <el-form-item label="产线" prop="plNoKey">
            <el-tree-select
              v-model="searchForm.plNoKey"
              :props="treeProps"
              :data="treeData"
              check-strictly
              :render-after-expand="false"
              placeholder="默认全部产线"
              class="search-tree-select"
              value-key="key"
              multiple
              clearable
            />
          </el-form-item>
          <el-form-item label="设备号" prop="equipmentNo">
            <el-input
              v-model="searchForm.equipmentNo"
              placeholder="请输入设备号"
              clearable
              class="search-input"
            ></el-input>
          </el-form-item>
          <el-form-item label="设备故障模式" prop="stopReasonName">
            <el-select
              v-model="searchForm.stopReasonName"
              placeholder="请选择故障模式"
              multiple
              clearable
              class="search-select"
            >
              <el-option
                v-for="item in attrCnts.faultModeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围" prop="vDay">
            <el-date-picker
              v-model="searchForm.vDay"
              type="daterange"
              start-placeholder="开始日期"
              range-separator="至"
              end-placeholder="结束日期"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              class="search-datepicker"
            >
            </el-date-picker>
          </el-form-item>
          <div class="search-buttons">
            <el-form-item>
              <el-button type="primary" @click="fun_search(searchFormRef)"
                >查询</el-button
              >
              <el-button @click="fun_reset(searchFormRef)">重置</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 布局 2: 内容区域 (使用子组件) -->
    <content-area :data-list="dataList"></content-area>

    <!-- 布局 3: 分析功能区域 -->
    <div class="layout-analysis">
      <el-card class="analysis-card">
        <template #header>
          <div>
            <span>分析功能</span>
          </div>
        </template>
        <div>
          <el-input
            type="textarea"
            v-model="analysisText"
            :rows="3"
            readonly
            resize="none"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
// 导入 Vue 相关模块和 Element Plus 组件
import { defineComponent, reactive, ref, Ref } from "vue";
import {
  ElMessage,
  ElForm,
  ElTreeSelect,
  FormInstance,
  FormRules,
} from "element-plus";
// 导入 Dayjs 用于日期处理
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 导入 Dayjs 中文语言包
// 导入子组件 ContentArea 及其导出的类型 FaultModeItem
import ContentArea, { FaultModeItem } from "./components/ContentArea.vue";

// 设置 Dayjs 全局本地化为中文
dayjs.locale("zh-cn");

// --- 类型定义 ---

// 产线树节点接口
interface PlTreeNode {
  parent_plno: string | null; // 父节点编号
  plname: string; // 产线名称
  plno: string; // 产线编号
  key?: string; // 树选择器使用的 key
  isLeaf?: boolean; // 是否叶子节点
  children?: PlTreeNode[] | null; // 子节点
}

// 故障模式下拉选项接口
interface FaultModeOption {
  label: string; // 显示文本
  value: string; // 选项值
}

// 筛选表单数据接口
interface SearchForm {
  plNoKey: string[]; // 产线 key 数组
  equipmentNo: string; // 设备号
  stopReasonName: string[]; // 故障模式名称数组
  vDay: [string, string]; // 日期范围 [开始, 结束]
}

// 通用 API 响应接口
interface ApiResponse<T> {
  success: boolean; // 是否成功
  message: string; // 消息
  code: number; // 状态码
  result: T; // 结果数据
  timestamp: number; // 时间戳
}

// 故障模式列表 API 响应结果类型 (使用导入的类型)
type ListDataResult = FaultModeItem[];

// 产线树 API 响应结果类型
type TreeDataResult = PlTreeNode[];

// 故障模式类型 API 响应结果接口
interface StopReasonTypeItem {
  stopReasonType: string; // 故障模式类型名称
}
// 故障模式类型 API 响应结果类型
type StopReasonDataResult = StopReasonTypeItem[];

// --- 组件定义 ---
export default defineComponent({
  name: "demo05", // 组件名称
  components: {
    ContentArea, // 注册内容区域子组件
  },
  // --- Composition API setup ---
  setup() {
    // 定义响应式状态：页面加载状态
    const loading: Ref<boolean> = ref(true);

    // 定义产线树选择器的配置
    const treeProps = {
      label: "plname", // 节点显示文本字段
      children: "children", // 子节点字段
    };
    // 定义响应式状态：产线树数据
    const treeData: Ref<PlTreeNode[]> = ref([]);

    // 定义响应式引用：筛选表单实例
    const searchFormRef: Ref<FormInstance | null> = ref(null);
    // 定义响应式状态：筛选表单验证规则
    const searchFormRules = reactive<FormRules>({
      vDay: [ // 日期范围验证规则
        {
          type: "array",
          required: true,
          message: "请选择日期范围",
          trigger: "change",
        },
      ],
    });

    // 返回 setup 中定义的响应式变量和配置，供模板使用
    return {
      loading,
      treeProps,
      treeData,
      searchFormRules,
      searchFormRef,
    };
  },
  // --- Options API data ---
  data(): {
    // 属性容器，存放下拉选项等动态数据
    attrCnts: {
      faultModeOptions: FaultModeOption[]; // 故障模式下拉选项
    };
    // 筛选表单数据模型
    searchForm: SearchForm;
    // 传递给子组件的故障模式列表数据
    dataList: FaultModeItem[];
    // 分析功能区域显示的文本
    analysisText: string;
    // 模拟 API 返回数据 (实际应通过 API 获取)
    listData: ApiResponse<ListDataResult>; // 列表数据
    treeDataJson: ApiResponse<TreeDataResult>; // 树数据
    stopReasonData: ApiResponse<StopReasonDataResult>; // 故障类型数据
  } {
    return {
      // 初始化属性容器
      attrCnts: {
        faultModeOptions: [],
      },
      // 初始化筛选表单
      searchForm: {
        plNoKey: [],
        equipmentNo: "",
        stopReasonName: [],
        vDay: ["2000-01-01", "2026-01-01"], // 默认日期范围
      },
      // 初始化列表数据
      dataList: [],
      // 初始化分析文本
      analysisText:
        "炼铁厂2#高炉2024年6月机械磨损（25%）、异常表振动（45%）占比总和超80%，油脂劣化（5%）近三个月呈上升趋势。",

      // --- 模拟数据区 (实际应用中应移除或替换为 API 调用) ---
      listData: { success: true, message: "", code: 200, result: [ { bkReasonName: "其他", totalBreakTimeRate: "27.66%", totalBreakTime: 3596, }, { bkReasonName: "设计机构，构造有问题", totalBreakTimeRate: "19.58%", totalBreakTime: 2545, }, { bkReasonName: "点检方法，判定基准有问题", totalBreakTimeRate: "8.54%", totalBreakTime: 1110, }, { bkReasonName: "修理，维护方法有问题", totalBreakTimeRate: "7.38%", totalBreakTime: 960, }, { bkReasonName: "自然劣化(预测困难)", totalBreakTimeRate: "7.04%", totalBreakTime: 915, }, { bkReasonName: "材质缺陷(质量，热处理，表面处理)", totalBreakTimeRate: "7.02%", totalBreakTime: 913, }, { bkReasonName: "点检周期有问题", totalBreakTimeRate: "3.50%", totalBreakTime: 455, }, { bkReasonName: "修理维护周期不适当", totalBreakTimeRate: "3.18%", totalBreakTime: 413, }, { bkReasonName: "机种，型式选定有问题", totalBreakTimeRate: "2.64%", totalBreakTime: 343, }, { bkReasonName: "运转操作技能，知识不足", totalBreakTimeRate: "1.84%", totalBreakTime: 239, }, { bkReasonName: "异物侵入", totalBreakTimeRate: "1.48%", totalBreakTime: 193, }, { bkReasonName: "尺寸，强度，容量有问题", totalBreakTimeRate: "1.24%", totalBreakTime: 161, }, { bkReasonName: "组装，配合施工技术差", totalBreakTimeRate: "1.18%", totalBreakTime: 154, }, { bkReasonName: "松动松弛", totalBreakTimeRate: "1.08%", totalBreakTime: 140, }, { bkReasonName: "组装，配合方法上有问题", totalBreakTimeRate: "1.05%", totalBreakTime: 137, }, { bkReasonName: "疲劳损伤", totalBreakTimeRate: "0.96%", totalBreakTime: 125, }, { bkReasonName: "油质劣化", totalBreakTimeRate: "0.91%", totalBreakTime: 118, }, { bkReasonName: "异常振动", totalBreakTimeRate: "0.74%", totalBreakTime: 96, }, { bkReasonName: "配合加工有问题", totalBreakTimeRate: "0.69%", totalBreakTime: 90, }, { bkReasonName: "异物混入", totalBreakTimeRate: "0.68%", totalBreakTime: 88, }, { bkReasonName: "机械磨损", totalBreakTimeRate: "0.46%", totalBreakTime: 60, }, { bkReasonName: "异常温度", totalBreakTimeRate: "0.38%", totalBreakTime: 50, }, { bkReasonName: "安装，拆卸方法上有问题", totalBreakTimeRate: "0.23%", totalBreakTime: 30, }, { bkReasonName: "机械剥离", totalBreakTimeRate: "0.22%", totalBreakTime: 29, }, { bkReasonName: "误运转操作", totalBreakTimeRate: "0.19%", totalBreakTime: 25, }, { bkReasonName: "温度(在计划设计时不能预测)", totalBreakTimeRate: "0.12%", totalBreakTime: 15, }, ], timestamp: 1745118762285, },
      treeDataJson: { success: true, message: "", code: 200, result: [ { parent_plno: null, plname: "根节点", children: [ { parent_plno: "A1", plname: "厂内运输处", children: [ { parent_plno: "W1", plname: "厂内运输处-球团工场", plno: "W101", }, ], plno: "W1", }, { parent_plno: "A1", plname: "烧结厂", children: [ { parent_plno: "W2", plname: "烧结厂-园区 1#烧结", plno: "W201", }, { parent_plno: "W2", plname: "烧结厂-园区 2#烧结", plno: "W202", }, { parent_plno: "W2", plname: "烧结厂-园区 3#烧结", plno: "W203", }, { parent_plno: "W2", plname: "烧结厂-园区 4#烧结", plno: "W204", }, { parent_plno: "W2", plname: "烧结厂-西区 3#烧结", plno: "W205", }, { parent_plno: "W2", plname: "烧结厂-西区 4#烧结", plno: "W206", }, ], plno: "W2", }, { parent_plno: "A1", plname: "炼铁厂", children: [ { parent_plno: "W3", plname: "炼铁厂-1#高炉", plno: "W301", }, { parent_plno: "W3", plname: "炼铁厂-2#高炉", plno: "W302", }, { parent_plno: "W3", plname: "炼铁厂-3#高炉", plno: "W303", }, { parent_plno: "W3", plname: "炼铁厂-4#高炉", plno: "W304", }, { parent_plno: "W3", plname: "炼铁厂-铸造高炉", plno: "W305", }, ], plno: "W3", }, { parent_plno: "A1", plname: "炼钢一厂", children: [ { parent_plno: "W4", plname: "炼钢一厂-1#中心烧嘴窑", plno: "W401", }, { parent_plno: "W4", plname: "炼钢一厂-2#中心烧嘴窑", plno: "W402", }, { parent_plno: "W4", plname: "炼钢一厂-3#中心烧嘴窑", plno: "W403", }, { parent_plno: "W4", plname: "炼钢一厂-4#中心烧嘴窑", plno: "W404", }, { parent_plno: "W4", plname: "炼钢一厂-5#中心烧嘴窑", plno: "W405", }, { parent_plno: "W4", plname: "炼钢一厂-6#中心烧嘴窑", plno: "W406", }, { parent_plno: "W4", plname: "炼钢一厂-7#中心烧嘴窑", plno: "W407", }, { parent_plno: "W4", plname: "炼钢一厂-1#套筒窑", plno: "W408", }, { parent_plno: "W4", plname: "炼钢一厂-2#套筒窑", plno: "W409", }, { parent_plno: "W4", plname: "炼钢一厂-3#转炉", plno: "W410", }, { parent_plno: "W4", plname: "炼钢一厂-4#转炉", plno: "W411", }, { parent_plno: "W4", plname: "炼钢一厂-5#转炉", plno: "W412", }, { parent_plno: "W4", plname: "炼钢一厂-1#连铸机", plno: "W413", }, { parent_plno: "W4", plname: "炼钢一厂-2#连铸机", plno: "W414", }, { parent_plno: "W4", plname: "炼钢一厂-3#连铸机", plno: "W415", }, { parent_plno: "W4", plname: "炼钢一厂-4#连铸机", plno: "W416", }, { parent_plno: "W4", plname: "炼钢一厂-3#精炼炉", plno: "W417", }, ], plno: "W4", }, { parent_plno: "A1", plname: "公用设施处", children: [ { parent_plno: "W5", plname: "公用设施处-12000 制氧", plno: "W503", }, { parent_plno: "W5", plname: "公用设施处-15000 制氧", plno: "W504", }, { parent_plno: "W5", plname: "公用设施处-20000 制氧", plno: "W505", }, { parent_plno: "W5", plname: "公用设施处-30000 制氧", plno: "W506", }, { parent_plno: "W5", plname: "公用设施处-50000 制氧", plno: "W507", }, { parent_plno: "W5", plname: "公用设施处-亚临界一期", plno: "W508", }, { parent_plno: "W5", plname: "公用设施处-亚临界二期", plno: "W509", }, { parent_plno: "W5", plname: "公用设施处-亚临界三期", plno: "W510", }, { parent_plno: "W5", plname: "公用设施处-1#机组蒸汽发电", plno: "W511", }, { parent_plno: "W5", plname: "公用设施处-2#机组尾气发电", plno: "W512", }, { parent_plno: "W5", plname: "公用设施处-3#机组蒸汽发电", plno: "W513", }, { parent_plno: "W5", plname: "公用设施处-园区烧结余热 1#机组", plno: "W514", }, { parent_plno: "W5", plname: "公用设施处-园区烧结余热 2#机组", plno: "W515", }, ], plno: "W5", }, { parent_plno: "A1", plname: "炼钢二厂", children: [ { parent_plno: "W8", plname: "炼钢二厂-1#转炉", plno: "W801", }, { parent_plno: "W8", plname: "炼钢二厂-2#转炉", plno: "W802", }, { parent_plno: "W8", plname: "炼钢二厂-1#连铸机", plno: "W803", }, { parent_plno: "W8", plname: "炼钢二厂-2#连铸机", plno: "W804", }, { parent_plno: "W8", plname: "炼钢二厂-3#连铸机", plno: "W805", }, { parent_plno: "W8", plname: "炼钢二厂-1#精炼炉", plno: "W806", }, { parent_plno: "W8", plname: "炼钢二厂-2#精炼炉", plno: "W807", }, ], plno: "W8", }, { parent_plno: "A1", plname: "水钢渣厂", children: [ { parent_plno: "W9", plname: "水钢渣厂-1#生产线", plno: "W901", }, { parent_plno: "W9", plname: "水钢渣厂-2#生产线", plno: "W902", }, { parent_plno: "W9", plname: "水钢渣厂-3#生产线", plno: "W903", }, { parent_plno: "W9", plname: "水钢渣厂-150 万立磨", plno: "W904", }, { parent_plno: "W9", plname: "水钢渣厂-钢渣立磨", plno: "W905", }, { parent_plno: "W9", plname: "水钢渣厂-磁选线", plno: "W906", }, { parent_plno: "W9", plname: "水钢渣厂-棒磨", plno: "W907", }, ], plno: "W9", }, { parent_plno: "A1", plname: "焊管厂", children: [ { parent_plno: "WA", plname: "焊管厂-螺旋焊车间", plno: "WA01", }, { parent_plno: "WA", plname: "焊管厂-直缝焊车间", plno: "WA02", }, { parent_plno: "WA", plname: "焊管厂-纵剪车间", plno: "WA03", }, { parent_plno: "WA", plname: "焊管厂-热镀锌车间", plno: "WA04", }, { parent_plno: "WA", plname: "焊管厂-公辅车间", plno: "WA05", }, ], plno: "WA", }, { parent_plno: "A1", plname: "轧钢一厂", children: [ { parent_plno: "Y2", plname: "轧钢一厂-二车间", plno: "Y201", }, { parent_plno: "Y2", plname: "轧钢一厂-三车间", plno: "Y202", }, { parent_plno: "Y2", plname: "轧钢一厂-四车间", plno: "Y203", }, ], plno: "Y2", }, { parent_plno: "A1", plname: "轧钢二厂", children: [ { parent_plno: "Y3", plname: "轧钢二厂-单高棒", plno: "Y301", }, { parent_plno: "Y3", plname: "轧钢二厂-双高棒", plno: "Y302", }, { parent_plno: "Y3", plname: "轧钢二厂-五车间", plno: "Y303", }, ], plno: "Y3", }, { parent_plno: "A1", plname: "带钢厂", children: null, plno: "Y4", }, { parent_plno: "A1", plname: "热轧厂", children: null, plno: "Y5", }, { parent_plno: "A1", plname: "冷轧厂", children: [ { parent_plno: "Y8", plname: "冷轧厂-酸轧", plno: "Y801", }, { parent_plno: "Y8", plname: "冷轧厂-镀锌一线", plno: "Y802", }, { parent_plno: "Y8", plname: "冷轧厂-镀锌二线", plno: "Y803", }, { parent_plno: "Y8", plname: "冷轧厂-镀锌三线", plno: "Y804", }, { parent_plno: "Y8", plname: "冷轧厂-罩平", plno: "Y805", }, { parent_plno: "Y8", plname: "冷轧厂-彩涂", plno: "Y806", }, { parent_plno: "Y8", plname: "冷轧厂-酸平", plno: "Y807", }, { parent_plno: "Y8", plname: "冷轧厂-热镀", plno: "Y808", }, ], plno: "Y8", }, ], plno: "A1", }, ], timestamp: 1745118762288, },
      stopReasonData: { success: true, message: "", code: 200, result: [ { stopReasonType: "质量原因", }, { stopReasonType: "设计原因", }, { stopReasonType: "维修原因", }, { stopReasonType: "管理原因", }, { stopReasonType: "操作原因", }, { stopReasonType: "其他原因", }, ], timestamp: 1745118762197, },
    };
  },
  // --- 生命周期钩子 ---
  mounted() {
    // 组件挂载后执行初始化数据查询
    this.query(); // 查询列表数据
    this.queryTree(); // 查询树数据
    this.queryStopReasonType(); // 查询故障类型下拉选项
  },
  // --- 方法定义 ---
  methods: {
    /**
     * 查询故障模式列表数据 (模拟)
     * 更新 dataList 状态
     */
    query(): void {
      this.loading = true; // 开始加载
      // 模拟 API 调用成功
      if (this.listData.success) {
        // 对模拟数据按占比降序排序
        const sortedData = [...this.listData.result].sort((a, b) => {
          const valueA = parseFloat(
            String(a.totalBreakTimeRate || "0").replace("%", "")
          );
          const valueB = parseFloat(
            String(b.totalBreakTimeRate || "0").replace("%", "")
          );
          return valueB - valueA; // 降序
        });
        this.dataList = sortedData; // 更新组件状态
      } else {
        // 模拟 API 调用失败
        ElMessage.error(
          this.listData.message || "获取列表数据失败 (模拟)"
        );
      }
      this.loading = false; // 结束加载
    },

    /**
     * 查询产线树数据 (模拟)
     * 更新 treeData 状态
     */
    queryTree(): void {
      this.loading = true;
      if (this.treeDataJson.success) {
        // 递归函数：为树节点添加 key 和 isLeaf 属性
        const processNode = (node: PlTreeNode): void => {
          node.key = node.plno; // 使用 plno 作为唯一 key
          node.isLeaf = !node.children || node.children.length === 0; // 判断是否为叶子节点
          if (node.children && node.children.length > 0) {
            node.children.forEach(processNode); // 递归处理子节点
          }
        };
        // 处理模拟数据（假设根节点下有子节点）
        const rootObject = this.treeDataJson.result[0];
        if (rootObject && rootObject.children) {
          rootObject.children.forEach(processNode);
          this.treeData = rootObject.children; // 更新树数据
        } else {
          this.treeData = [];
        }
      } else {
        ElMessage.error(
          this.treeDataJson.message || "获取树形列表失败 (模拟)"
        );
        this.treeData = [];
      }
      this.loading = false;
    },

    /**
     * 查询故障模式下拉选项数据 (模拟)
     * 更新 attrCnts.faultModeOptions 状态
     */
    queryStopReasonType(): void {
      this.loading = true;
      if (this.stopReasonData.success) {
        // 将 API 返回的故障类型列表转换为下拉选项格式
        this.attrCnts.faultModeOptions = this.stopReasonData.result.map(
          (item): FaultModeOption => {
            return { label: item.stopReasonType, value: item.stopReasonType };
          }
        );
      } else {
        ElMessage.error(
          this.stopReasonData.message || "获取下拉框选项失败 (模拟)"
        );
        this.attrCnts.faultModeOptions = [];
      }
      this.loading = false;
    },

    /**
     * "查询"按钮点击事件处理函数
     * @param formEl - 表单实例引用
     */
    async fun_search(formEl: FormInstance | null): Promise<void> {
      if (!formEl) return; // 检查表单实例是否存在
      try {
        // 异步触发表单验证
        const valid = await formEl.validate();
        if (valid) {
          // 验证通过，执行查询
          this.query();
        }
        // 验证失败时 validate 会 reject，自动进入 catch
      } catch (error) {
        // 捕获验证失败或查询过程中的其他错误
        console.warn("表单验证失败或查询出错:", error);
        ElMessage.warning("请检查筛选条件是否填写正确");
      }
    },

    /**
     * "重置"按钮点击事件处理函数
     * @param formEl - 表单实例引用
     */
    fun_reset(formEl: FormInstance | null): void {
      if (!formEl) return; // 检查表单实例是否存在
      formEl.resetFields(); // 调用 Element Plus 表单的重置方法
      this.query(); // 重置后重新查询数据
    },
  },
});
</script>

<style scoped>
/* --- 页面整体与根容器 --- */
.root-container {
  display: flex; /* Flex 布局 */
  flex-direction: column; /* 垂直排列 */
  height: 100vh; /* 占满视口高度 */
  font-size: 14px; /* 基础字号 */
  overflow: hidden; /* 防止自身滚动 */
  background-color: #f5f7fa; /* 背景色 */
  padding: 20px; /* 内边距 */
  box-sizing: border-box; /* padding 包含在宽高内 */
}

/* --- 通用卡片样式调整 --- */
.root-container .el-card__header {
  padding: 15px 20px; /* 头部内边距 */
  border-bottom: 1px solid #ebeef5; /* 头部下边框 */
  font-weight: bold; /* 头部文字加粗 */
}
.root-container .el-card__body {
  padding: 20px; /* 主体内边距 */
}

/* --- 布局 1: 筛选区域 --- */
.root-container .layout-search {
  flex-shrink: 0; /* 防止被压缩 */
  margin-bottom: 15px; /* 与下方内容间距 */
  padding: 20px; /* 自身内边距 */
  background-color: #fff; /* 背景色 */
  border-radius: 4px; /* 圆角 */
  border: 1px solid #ebeef5; /* 边框 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 阴影 */
}
.root-container .layout-search .search-filters {
  display: flex; /* Flex 布局 */
  flex-wrap: wrap; /* 自动换行 */
  gap: 15px 20px; /* 行列间距 */
  align-items: flex-start; /* 顶部对齐 */
}
.root-container .layout-search .search-filters .el-form-item {
  margin-bottom: 0; /* 移除表单项默认下边距 */
}
/* 筛选控件宽度 */
.root-container .layout-search .search-tree-select { width: 240px; }
.root-container .layout-search .search-input { width: 220px; }
.root-container .layout-search .search-select { width: 220px; }
.root-container .layout-search .search-datepicker { width: 280px; }
.root-container .layout-search .search-buttons {} /* 按钮容器样式 (可选) */
.root-container .layout-search .search-buttons .el-form-item { margin-bottom: 0; }

/* --- 布局 2: 内容区域 --- */
/* .layout-content 样式由子组件 ContentArea.vue 控制 */

/* --- 布局 3: 分析功能区域 --- */
.root-container .layout-analysis {
  flex-shrink: 0; /* 防止被压缩 */
}
.root-container .layout-analysis .analysis-card {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
/* 分析文本域样式 (使其看起来像普通文本) */
.root-container .layout-analysis .el-textarea__inner {
  border: none;
  box-shadow: none;
  background-color: transparent;
  padding: 5px 0;
  font-size: inherit;
  color: #606266;
  cursor: default;
  resize: none;
}
</style>
