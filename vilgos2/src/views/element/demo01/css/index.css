/* ==========================================================================
   通用页面和容器样式 (Scoped to .root-container)
   ========================================================================== */

/* 页面根容器 */
.root-container {
  /* 页面内容与浏览器边缘的内边距 */
  padding: 20px;
  /* 基础字体大小 */
  font-size: 14px;
  /* 防止因内部元素意外溢出导致横向滚动条 */
  overflow-x: hidden;
  /* 可以设置一个浅灰色背景，与卡片区分 */
  background-color: #f5f7fa;
  /*!* --- 添加缩放样式 --- *!*/
  /*!* 原始布局 1920*929，调整后布局 1710*832 *!*/
  /*!* 注意：这可能会导致模糊和交互问题 *!*/
  /*transform-origin: top left;*/
  /*!* 设置缩放基点为左上角 *!*/
  /*transform: scale(0.89);*/
  /*!* 应用计算出的缩放因子 *!*/
}

/* 通用 Element Plus 卡片头部样式 */
.root-container .el-card__header {
  /* 卡片头部的内边距 */
  padding: 15px 20px;
  /* 头部下方的分隔线 */
  border-bottom: 1px solid #ebeef5;
  /* 头部标题加粗 */
  font-weight: bold;
}

/* 通用 Element Plus 卡片主体样式 */
.root-container .el-card__body {
  /* 卡片主体的内边距 */
  padding: 20px;
}

/* ==========================================================================
   布局 1: 筛选区域 (.layout-search)
   ========================================================================== */

/* 筛选区域外层容器 - 应用原 .search-form-flex 的样式 */
.root-container .layout-search {
  /* 与下方内容区域的间距 */
  margin-bottom: 15px;
  /* 筛选区域内部的内边距 */
  padding: 20px;
  /* 白色背景 */
  background-color: #fff;
  /* 圆角 */
  border-radius: 4px;
  /* 浅灰色边框 */
  border: 1px solid #ebeef5;
  /* 轻微阴影效果 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 筛选条件容器 (包含所有 el-form-item 和按钮组) */
.root-container .layout-search .search-filters {
  /* 使用 Flexbox 布局 */
  display: flex;
  /* 允许筛选条件换行 */
  flex-wrap: wrap;
  /* 行间距 15px，列间距 20px */
  gap: 15px 20px;
  /* 垂直居中对齐筛选条件 */
  align-items: center;
}

/* 筛选条件表单项 */
.root-container .layout-search .search-filters .el-form-item {
  /* 移除 Element Plus 表单项默认的下外边距，使用 gap 控制间距 */
  margin-bottom: 0;
}

/* 设置具体筛选控件的宽度 */
/* 产线树形选择器宽度 */
.root-container .layout-search .search-tree-select {
  width: 240px;
}

/* 设备号输入框宽度 */
.root-container .layout-search .search-input {
  width: 220px;
}

/* 故障模式选择器宽度 */
.root-container .layout-search .search-select {
  width: 220px;
}

/* 日期范围选择器宽度 */
.root-container .layout-search .search-datepicker {
  width: 280px;
}

/* 查询/重置按钮容器 - 现在是 flex 项目 */
.root-container .layout-search .search-buttons {
  margin-left: 0px;
}

/* 查询/重置按钮的表单项 */
.root-container .layout-search .search-buttons .el-form-item {
  /* 移除 Element Plus 表单项默认的下外边距 */
  margin-bottom: 0;
}

/* ==========================================================================
   布局 2: 内容区域 (.layout-content)
   ========================================================================== */

/* 内容区域外层容器 (包含左右两列) */
.root-container .layout-content {
  /* 与下方分析区域的间距 */
  margin-bottom: 15px;
}

/* 左右卡片 (列表卡片和图表卡片) 的通用基础样式 */
.root-container .layout-content .list-card,
.root-container .layout-content .chart-card {
  /* 白色背景 */
  background-color: #fff;
  /* 圆角 */
  border-radius: 4px;
  /* 浅灰色边框 */
  border: 1px solid #ebeef5;
  /* 轻微阴影效果 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  /* 注意：卡片的具体高度由内部元素决定或通过 JS 设置 */
}

/* 左侧列表区域的表格样式 */
.root-container .layout-content .list-card .el-table {
  /* 确保表格宽度充满其父容器 (el-card__body) */
  width: 100%;
  /* 表格高度由 Vue 模板中的 height 属性控制，以实现固定表头和内部滚动 */
  height: 515px;
}

/* 右侧图表区域的图表容器样式 */
.root-container .layout-content .chart-container {
  /* 图表宽度充满其父容器 (el-card__body) */
  width: 100%;
  /* 图表固定高度 */
  height: 515px;
}

/* ==========================================================================
   布局 3: 分析功能区域 (.layout-analysis)
   ========================================================================== */

/* 分析功能区域卡片样式 */
.root-container .layout-analysis .analysis-card {
  /* 白色背景 */
  background-color: #fff;
  /* 圆角 */
  border-radius: 4px;
  /* 浅灰色边框 */
  border: 1px solid #ebeef5;
  /* 轻微阴影效果 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  /* margin-top 由 layout-content 的 margin-bottom 控制 */
}

/* 分析功能区域文本域样式 (使其看起来像普通文本) */
.root-container .layout-analysis .el-textarea__inner {
  /* 移除边框 */
  border: none;
  /* 移除阴影 */
  box-shadow: none;
  /* 透明背景 */
  background-color: transparent;
  /* 调整内部上下边距 */
  padding: 5px 0;
  /* 继承父级字体大小 */
  font-size: inherit;
  /* 文本颜色 */
  color: #606266;
  /* 默认鼠标样式 */
  cursor: default;
  /* 禁止调整大小 (虽然 Vue 模板中已有 :resize="none") */
  resize: none;
}