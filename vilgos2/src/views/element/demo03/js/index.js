import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus';
// dayjs
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'
// 导入子组件
import ContentArea from '../components/ContentArea.vue'; // 导入内容区域子组件

dayjs.locale('zh-cn') // 设置 dayjs 本地化为中文

export default {
  name: 'demo03', // 组件名称 (应与文件夹名一致)
  components: { // 注册子组件
    ContentArea
  },
  setup() {
    // --- Setup 中定义的响应式变量 ---
    const loading = ref(true); // 控制页面加载状态的 V-Loading 指令

    // 产线树形选择器的配置项
    const treeProps = {
      label: 'plname', // 指定节点标签为节点对象的 'plname' 属性
      children: 'children', // 指定子树为节点对象的 'children' 属性
    };
    const treeData = ref([]); // 产线树形选择器的数据源

    // 筛选表单的引用和验证规则
    const searchFormRef = ref(''); // 筛选表单的模板引用 (用于访问表单实例)
    const searchFormRules = reactive({ // 筛选表单的验证规则
      // equipmentNo: [ // 设备号验证规则 (示例，当前注释掉)
      //     {required: true, message: '请输入设备号', trigger: 'blur'},
      // ],
      vDay: [ // 日期范围验证规则
        { type: 'array', required: true, message: '请选择日期范围', trigger: 'change' }
      ],
    });

    // 从 setup 返回响应式变量，使其在模板和 Options API 中可用
    return {
      loading,
      treeProps,
      treeData,
      searchFormRules,
      searchFormRef,
    };
  },
  data() {
    // --- Options API 中定义的响应式数据 ---
    return {
      // 通用属性或配置
      attrCnts: {
        faultModeOptions: [], // 设备故障模式下拉框的选项
      },
      // 筛选表单的数据模型
      searchForm: {
        plNoKey: [], // 产线选择 (el-tree-select 的 v-model)
        equipmentNo: '', // 设备号输入 (el-input 的 v-model)
        stopReasonName: [], // 设备故障模式选择 (el-select 的 v-model)
        vDay: [ // 日期范围选择 (el-date-picker 的 v-model)
          // dayjs().subtract(3, 'month').format('YYYY-MM-DD'), // 示例：默认最近三个月
          // dayjs().format('YYYY-MM-DD')
          '2000-01-01', // 默认开始日期
          '2026-01-01'  // 默认结束日期
        ],
      },
      // 传递给子组件 ContentArea 的数据列表
      dataList: [],
      // 分析功能区域的文本模型
      analysisText: '炼铁厂2#高炉2024年6月机械磨损（25%）、异常表振动（45%）占比总和超80%，油脂劣化（5%）近三个月呈上升趋势。',

      // --- 模拟数据 (实际应用中应通过 API 获取) ---
      listData: { // 模拟故障模式列表接口返回数据
        "success": true,
        "message": "",
        "code": 200,
        "result": [
          {
            "bkReasonName": "其他",
            "totalBreakTimeRate": "27.66%",
            "totalBreakTime": 3596
          },
          {
            "bkReasonName": "设计机构，构造有问题",
            "totalBreakTimeRate": "19.58%",
            "totalBreakTime": 2545
          },
          {
            "bkReasonName": "点检方法，判定基准有问题",
            "totalBreakTimeRate": "8.54%",
            "totalBreakTime": 1110
          },
          {
            "bkReasonName": "修理，维护方法有问题",
            "totalBreakTimeRate": "7.38%",
            "totalBreakTime": 960
          },
          {
            "bkReasonName": "自然劣化(预测困难)",
            "totalBreakTimeRate": "7.04%",
            "totalBreakTime": 915
          },
          {
            "bkReasonName": "材质缺陷(质量，热处理，表面处理)",
            "totalBreakTimeRate": "7.02%",
            "totalBreakTime": 913
          },
          {
            "bkReasonName": "点检周期有问题",
            "totalBreakTimeRate": "3.50%",
            "totalBreakTime": 455
          },
          {
            "bkReasonName": "修理维护周期不适当",
            "totalBreakTimeRate": "3.18%",
            "totalBreakTime": 413
          },
          {
            "bkReasonName": "机种，型式选定有问题",
            "totalBreakTimeRate": "2.64%",
            "totalBreakTime": 343
          },
          {
            "bkReasonName": "运转操作技能，知识不足",
            "totalBreakTimeRate": "1.84%",
            "totalBreakTime": 239
          },
          {
            "bkReasonName": "异物侵入",
            "totalBreakTimeRate": "1.48%",
            "totalBreakTime": 193
          },
          {
            "bkReasonName": "尺寸，强度，容量有问题",
            "totalBreakTimeRate": "1.24%",
            "totalBreakTime": 161
          },
          {
            "bkReasonName": "组装，配合施工技术差",
            "totalBreakTimeRate": "1.18%",
            "totalBreakTime": 154
          },
          {
            "bkReasonName": "松动松弛",
            "totalBreakTimeRate": "1.08%",
            "totalBreakTime": 140
          },
          {
            "bkReasonName": "组装，配合方法上有问题",
            "totalBreakTimeRate": "1.05%",
            "totalBreakTime": 137
          },
          {
            "bkReasonName": "疲劳损伤",
            "totalBreakTimeRate": "0.96%",
            "totalBreakTime": 125
          },
          {
            "bkReasonName": "油质劣化",
            "totalBreakTimeRate": "0.91%",
            "totalBreakTime": 118
          },
          {
            "bkReasonName": "异常振动",
            "totalBreakTimeRate": "0.74%",
            "totalBreakTime": 96
          },
          {
            "bkReasonName": "配合加工有问题",
            "totalBreakTimeRate": "0.69%",
            "totalBreakTime": 90
          },
          {
            "bkReasonName": "异物混入",
            "totalBreakTimeRate": "0.68%",
            "totalBreakTime": 88
          },
          {
            "bkReasonName": "机械磨损",
            "totalBreakTimeRate": "0.46%",
            "totalBreakTime": 60
          },
          {
            "bkReasonName": "异常温度",
            "totalBreakTimeRate": "0.38%",
            "totalBreakTime": 50
          },
          {
            "bkReasonName": "安装，拆卸方法上有问题",
            "totalBreakTimeRate": "0.23%",
            "totalBreakTime": 30
          },
          {
            "bkReasonName": "机械剥离",
            "totalBreakTimeRate": "0.22%",
            "totalBreakTime": 29
          },
          {
            "bkReasonName": "误运转操作",
            "totalBreakTimeRate": "0.19%",
            "totalBreakTime": 25
          },
          {
            "bkReasonName": "温度(在计划设计时不能预测)",
            "totalBreakTimeRate": "0.12%",
            "totalBreakTime": 15
          }
        ],
        "timestamp": 1745118762285
      },
      treeDataJson: {
        "success": true,
        "message": "",
        "code": 200,
        "result": [
          {
            "parent_plno": null,
            "plname": "根节点",
            "children": [
              {
                "parent_plno": "A1",
                "plname": "厂内运输处",
                "children": [
                  {
                    "parent_plno": "W1",
                    "plname": "厂内运输处-球团工场",
                    "plno": "W101"
                  }
                ],
                "plno": "W1"
              },
              {
                "parent_plno": "A1",
                "plname": "烧结厂",
                "children": [
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-园区 1#烧结",
                    "plno": "W201"
                  },
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-园区 2#烧结",
                    "plno": "W202"
                  },
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-园区 3#烧结",
                    "plno": "W203"
                  },
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-园区 4#烧结",
                    "plno": "W204"
                  },
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-西区 3#烧结",
                    "plno": "W205"
                  },
                  {
                    "parent_plno": "W2",
                    "plname": "烧结厂-西区 4#烧结",
                    "plno": "W206"
                  }
                ],
                "plno": "W2"
              },
              {
                "parent_plno": "A1",
                "plname": "炼铁厂",
                "children": [
                  {
                    "parent_plno": "W3",
                    "plname": "炼铁厂-1#高炉",
                    "plno": "W301"
                  },
                  {
                    "parent_plno": "W3",
                    "plname": "炼铁厂-2#高炉",
                    "plno": "W302"
                  },
                  {
                    "parent_plno": "W3",
                    "plname": "炼铁厂-3#高炉",
                    "plno": "W303"
                  },
                  {
                    "parent_plno": "W3",
                    "plname": "炼铁厂-4#高炉",
                    "plno": "W304"
                  },
                  {
                    "parent_plno": "W3",
                    "plname": "炼铁厂-铸造高炉",
                    "plno": "W305"
                  }
                ],
                "plno": "W3"
              },
              {
                "parent_plno": "A1",
                "plname": "炼钢一厂",
                "children": [
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-1#中心烧嘴窑",
                    "plno": "W401"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-2#中心烧嘴窑",
                    "plno": "W402"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-3#中心烧嘴窑",
                    "plno": "W403"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-4#中心烧嘴窑",
                    "plno": "W404"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-5#中心烧嘴窑",
                    "plno": "W405"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-6#中心烧嘴窑",
                    "plno": "W406"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-7#中心烧嘴窑",
                    "plno": "W407"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-1#套筒窑",
                    "plno": "W408"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-2#套筒窑",
                    "plno": "W409"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-3#转炉",
                    "plno": "W410"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-4#转炉",
                    "plno": "W411"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-5#转炉",
                    "plno": "W412"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-1#连铸机",
                    "plno": "W413"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-2#连铸机",
                    "plno": "W414"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-3#连铸机",
                    "plno": "W415"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-4#连铸机",
                    "plno": "W416"
                  },
                  {
                    "parent_plno": "W4",
                    "plname": "炼钢一厂-3#精炼炉",
                    "plno": "W417"
                  }
                ],
                "plno": "W4"
              },
              {
                "parent_plno": "A1",
                "plname": "公用设施处",
                "children": [
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-12000 制氧",
                    "plno": "W503"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-15000 制氧",
                    "plno": "W504"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-20000 制氧",
                    "plno": "W505"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-30000 制氧",
                    "plno": "W506"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-50000 制氧",
                    "plno": "W507"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-亚临界一期",
                    "plno": "W508"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-亚临界二期",
                    "plno": "W509"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-亚临界三期",
                    "plno": "W510"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-1#机组蒸汽发电",
                    "plno": "W511"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-2#机组尾气发电",
                    "plno": "W512"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-3#机组蒸汽发电",
                    "plno": "W513"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-园区烧结余热 1#机组",
                    "plno": "W514"
                  },
                  {
                    "parent_plno": "W5",
                    "plname": "公用设施处-园区烧结余热 2#机组",
                    "plno": "W515"
                  }
                ],
                "plno": "W5"
              },
              {
                "parent_plno": "A1",
                "plname": "炼钢二厂",
                "children": [
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-1#转炉",
                    "plno": "W801"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-2#转炉",
                    "plno": "W802"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-1#连铸机",
                    "plno": "W803"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-2#连铸机",
                    "plno": "W804"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-3#连铸机",
                    "plno": "W805"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-1#精炼炉",
                    "plno": "W806"
                  },
                  {
                    "parent_plno": "W8",
                    "plname": "炼钢二厂-2#精炼炉",
                    "plno": "W807"
                  }
                ],
                "plno": "W8"
              },
              {
                "parent_plno": "A1",
                "plname": "水钢渣厂",
                "children": [
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-1#生产线",
                    "plno": "W901"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-2#生产线",
                    "plno": "W902"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-3#生产线",
                    "plno": "W903"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-150 万立磨",
                    "plno": "W904"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-钢渣立磨",
                    "plno": "W905"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-磁选线",
                    "plno": "W906"
                  },
                  {
                    "parent_plno": "W9",
                    "plname": "水钢渣厂-棒磨",
                    "plno": "W907"
                  }
                ],
                "plno": "W9"
              },
              {
                "parent_plno": "A1",
                "plname": "焊管厂",
                "children": [
                  {
                    "parent_plno": "WA",
                    "plname": "焊管厂-螺旋焊车间",
                    "plno": "WA01"
                  },
                  {
                    "parent_plno": "WA",
                    "plname": "焊管厂-直缝焊车间",
                    "plno": "WA02"
                  },
                  {
                    "parent_plno": "WA",
                    "plname": "焊管厂-纵剪车间",
                    "plno": "WA03"
                  },
                  {
                    "parent_plno": "WA",
                    "plname": "焊管厂-热镀锌车间",
                    "plno": "WA04"
                  },
                  {
                    "parent_plno": "WA",
                    "plname": "焊管厂-公辅车间",
                    "plno": "WA05"
                  }
                ],
                "plno": "WA"
              },
              {
                "parent_plno": "A1",
                "plname": "轧钢一厂",
                "children": [
                  {
                    "parent_plno": "Y2",
                    "plname": "轧钢一厂-二车间",
                    "plno": "Y201"
                  },
                  {
                    "parent_plno": "Y2",
                    "plname": "轧钢一厂-三车间",
                    "plno": "Y202"
                  },
                  {
                    "parent_plno": "Y2",
                    "plname": "轧钢一厂-四车间",
                    "plno": "Y203"
                  }
                ],
                "plno": "Y2"
              },
              {
                "parent_plno": "A1",
                "plname": "轧钢二厂",
                "children": [
                  {
                    "parent_plno": "Y3",
                    "plname": "轧钢二厂-单高棒",
                    "plno": "Y301"
                  },
                  {
                    "parent_plno": "Y3",
                    "plname": "轧钢二厂-双高棒",
                    "plno": "Y302"
                  },
                  {
                    "parent_plno": "Y3",
                    "plname": "轧钢二厂-五车间",
                    "plno": "Y303"
                  }
                ],
                "plno": "Y3"
              },
              {
                "parent_plno": "A1",
                "plname": "带钢厂",
                "children": null,
                "plno": "Y4"
              },
              {
                "parent_plno": "A1",
                "plname": "热轧厂",
                "children": null,
                "plno": "Y5"
              },
              {
                "parent_plno": "A1",
                "plname": "冷轧厂",
                "children": [
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-酸轧",
                    "plno": "Y801"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-镀锌一线",
                    "plno": "Y802"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-镀锌二线",
                    "plno": "Y803"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-镀锌三线",
                    "plno": "Y804"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-罩平",
                    "plno": "Y805"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-彩涂",
                    "plno": "Y806"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-酸平",
                    "plno": "Y807"
                  },
                  {
                    "parent_plno": "Y8",
                    "plname": "冷轧厂-热镀",
                    "plno": "Y808"
                  }
                ],
                "plno": "Y8"
              }
            ],
            "plno": "A1"
          }
        ],
        "timestamp": 1745118762288
      },
      stopReasonData: {
        "success": true,
        "message": "",
        "code": 200,
        "result": [
          {
            "stopReasonType": "质量原因"
          },
          {
            "stopReasonType": "设计原因"
          },
          {
            "stopReasonType": "维修原因"
          },
          {
            "stopReasonType": "管理原因"
          },
          {
            "stopReasonType": "操作原因"
          },
          {
            "stopReasonType": "其他原因"
          }
        ],
        "timestamp": 1745118762197
      }
    };
  },
  mounted() {
    // --- 组件挂载后执行 ---
    this.query(); // 查询故障模式列表数据
    this.queryTree(); // 查询产线树数据
    this.queryStopReasonType(); // 查询故障模式下拉选项数据
  },
  methods: {
    // --- 数据查询方法 (当前使用模拟数据) ---

    /**
     * @description 查询故障模式列表数据 (模拟)
     * - 设置加载状态
     * - 使用本地模拟数据 `listData`
     * - 对数据按 `totalBreakTimeRate` 降序排序
     * - 更新 `dataList` (传递给子组件)
     * - 处理错误情况
     * - 结束加载状态
     */
    query() {
      let _this = this;
      _this.loading = true; // 开始加载状态
      // 实际应用中，这里会是 API 请求
      // 此处直接使用模拟数据 listData
      if (_this.listData.success) {
        // 对模拟数据按 totalBreakTimeRate 降序排序
        const sortedData = _this.listData.result.sort((a, b) => {
          const valueA = parseFloat(String(a.totalBreakTimeRate || '0').replace('%', ''));
          const valueB = parseFloat(String(b.totalBreakTimeRate || '0').replace('%', ''));
          return valueB - valueA; // 降序
        });
        _this.dataList = sortedData; // 更新 dataList，传递给子组件
      } else {
        ElMessage.error(_this.listData.message || '获取列表数据失败 (来自模拟数据)');
      }
      _this.loading = false; // 结束加载状态
    },

    /**
     * @description 查询产线树数据 (模拟)
     * - 设置加载状态
     * - 使用本地模拟数据 `treeDataJson`
     * - 递归处理树节点，添加 `key` 和 `isLeaf` 属性以适配 `el-tree-select`
     * - 更新 `treeData` (在 setup 中定义)
     * - 处理错误情况
     * - 结束加载状态
     */
    queryTree() {
      let _this = this;
      _this.loading = true;
      // 实际应用中，这里会是 API 请求
      // 此处直接使用模拟数据 treeDataJson
      if (_this.treeDataJson.success) {
        // 递归函数，为树节点添加 el-tree-select 需要的 key 和 isLeaf 属性
        const processNode = (node) => {
          node.key = node.plno; // 使用 plno 作为唯一 key
          node.isLeaf = !node.children || node.children.length === 0; // 判断是否为叶子节点
          if (node.children && node.children.length > 0) {
            node.children.forEach(processNode); // 递归处理子节点
          }
        };
        // 模拟数据包含一个根对象，我们需要其 children
        const rootObject = _this.treeDataJson.result[0];
        if (rootObject && rootObject.children) {
          rootObject.children.forEach(processNode); // 处理所有一级子节点
          _this.treeData = rootObject.children; // 更新 setup 中定义的 treeData ref
        } else {
          _this.treeData = []; // 如果没有子节点，设置为空数组
        }
      } else {
        ElMessage.error(_this.treeDataJson.message || '获取树形列表失败 (来自模拟数据)');
        _this.treeData = [];
      }
      _this.loading = false;
    },

    /**
     * @description 查询故障模式下拉选项数据 (模拟)
     * - 设置加载状态
     * - 使用本地模拟数据 `stopReasonData`
     * - 将数据映射为 `el-select` 需要的 `{ label, value }` 格式
     * - 更新 `attrCnts.faultModeOptions`
     * - 处理错误情况
     * - 结束加载状态
     */
    queryStopReasonType() {
      let _this = this;
      _this.loading = true;
      // 实际应用中，这里会是 API 请求
      // 此处直接使用模拟数据 stopReasonData
      if (_this.stopReasonData.success) {
        // 将模拟数据映射为 el-select 需要的 { label, value } 格式
        _this.attrCnts.faultModeOptions = _this.stopReasonData.result.map(item => {
          return { label: item.stopReasonType, value: item.stopReasonType };
        });
      } else {
        ElMessage.error(_this.stopReasonData.message || '获取下拉框选项失败 (来自模拟数据)');
        _this.attrCnts.faultModeOptions = [];
      }
      _this.loading = false;
    },

    // --- 事件处理方法 ---

    /**
     * @description 查询按钮点击事件处理
     * @param {Object} formEl - 表单实例 (通过模板引用传递)
     */
    async fun_search(formEl) {
      let _this = this;
      if (!formEl) { // 检查表单实例是否存在
        return;
      }
      // 触发表单验证
      await formEl.validate((valid) => {
        if (valid) { // 验证通过
          _this.query(); // 重新查询数据 (基于当前筛选条件，虽然模拟数据未实现过滤)
        } else { // 验证失败
          ElMessage.warning('请检查筛选参数是否符合要求');
        }
      })
    },

    /**
     * @description 重置按钮点击事件处理
     * @param {Object} formEl - 表单实例 (通过模板引用传递)
     */
    async fun_reset(formEl) {
      let _this = this;
      if (!formEl) { // 检查表单实例是否存在
        return;
      }
      formEl.resetFields(); // 重置表单到初始值
      _this.query(); // 重新查询数据 (显示所有数据)
    },
  },
};
