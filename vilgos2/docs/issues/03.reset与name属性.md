# Ant Design Vue 表单重置 (`resetFields`) 与 `name` 属性

`searchFormRef.value.resetFields()` 在 Ant Design Vue 中用于重置表单项。它依赖于 `<a-form-item>` 上的 `name` 属性来识别需要重置的字段。

**问题原因**:

`resetFields()` 方法通过 `<a-form-item>` 的 `name` 属性来查找 `a-form` 的 `model` (即 `searchForm`) 中对应的字段，并将它们重置为初始值。如果 `<a-form-item>` 没有 `name` 属性，`resetFields()` 就无法知道要重置哪些字段。

**解决方案**:

你需要为 `src/views/cjsbgzms/index.vue` 中 `<a-form>` 内的每个 `<a-form-item>` 添加 `name` 属性，其值应与 `searchForm` 对象中对应的属性名一致。

**示例**:

```vue
<a-form-item label="设备号：" :colon="false" name="equipmentNo">
  <a-input
    v-model:value="searchForm.equipmentNo"
    placeholder="请输入设备号"
    allow-clear
    class="custom-input"
  />
</a-form-item>

<a-form-item label="设备故障模式：" :colon="false" name="stopReasonName">
  <a-select
    v-model:value="searchForm.stopReasonName"
    mode="multiple"
    placeholder="请选择故障模式"
    :options="attrCnts.faultModeOptions"
    allow-clear
    class="custom-select"
  />
</a-form-item>

<a-form-item label="日期范围：" :colon="false" name="vDay">
  <a-range-picker
    :value="searchForm.vDay"
    @change="handleDateChange"
    :placeholder="['开始日期', '结束日期']"
    format="YYYY-MM-DD"
    :allowClear="true"
    :inputReadOnly="true"
    class="custom-range-picker"
  />
</a-form-item>
