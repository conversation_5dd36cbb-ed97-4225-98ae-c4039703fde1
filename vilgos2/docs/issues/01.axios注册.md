# axios 在 Vue 中的使用与注册

## 问题背景

在 Vue 3 项目中，我们经常需要使用 axios 进行 HTTP 请求。与 dayjs 等库不同，axios 不需要在 setup 函数中返回就可以在组件中使用。这引发了一个问题：为什么 axios 不需要注册到 setup 就可以直接使用，而 dayjs 需要？

## 原因分析

axios 不需要在 setup 函数中返回就可以直接使用，这是因为 axios 的使用方式与 dayjs 不同：

### 1. 使用场景不同

- **axios**：主要在 JavaScript 代码中使用，如方法内部、生命周期钩子等
- **dayjs**：在 Vue 模板中直接使用，如 `:value="[dayjs(searchForm.startDate), dayjs(searchForm.endDate)]"`

### 2. 作用域不同

- **axios**：在 JavaScript 代码中，您可以直接访问导入的模块，不需要通过 Vue 的模板系统
- **dayjs**：在 Vue 模板中，只能访问特定的内容（setup 返回的变量、data 中的数据、methods 中的方法等）

### 3. 代码示例

在组件中使用 axios（不需要在 setup 中返回）：

```javascript
import axios from 'axios';

export default {
  methods: {
    async list() {
      // ... 其他代码 ...
      await axios({
        url: this.wsUrl + '/cjsbgzms/list',
        method: 'POST',
        data: JSON.stringify(params),
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        }
      });
      // ... 其他代码 ...
    }
  }
}
```

在模板中使用 dayjs（需要在 setup 中返回）：

```html
<a-range-picker
  :value="[dayjs(searchForm.startDate), dayjs(searchForm.endDate)]"
  @change="handleDateChange"
  placeholder="请选择日期范围"
  style="width: 100%"
/>
```

## 技术原理解释

### Vue 的模板编译系统

Vue 的模板编译系统只会将特定的内容暴露给模板：

1. 在 `setup()` 函数中返回的变量
2. 在 `data()` 中定义的响应式数据
3. 在 `methods` 中定义的方法
4. 在 `computed` 中定义的计算属性

当您在模板中使用 `dayjs` 时，Vue 会尝试在模板上下文中查找 `dayjs` 函数。如果找不到，就会抛出 `_ctx.dayjs is not a function` 错误。

### JavaScript 模块系统

当您在 JavaScript 代码中导入一个模块（如 axios）时，该模块会在当前作用域中可用。您可以直接使用它，而不需要通过 Vue 的模板系统。

## 最佳实践

### 1. 在 JavaScript 代码中使用 axios

在 JavaScript 代码中（如方法内部、生命周期钩子等），您可以直接使用导入的 axios：

```javascript
import axios from 'axios';

export default {
  methods: {
    async fetchData() {
      try {
        const response = await axios.get('/api/data');
        this.data = response.data;
      } catch (error) {
        console.error('获取数据失败', error);
      }
    }
  }
}
```

### 2. 在模板中使用 axios

如果您需要在模板中使用 axios，您需要将其注册为方法或计算属性：

```javascript
import axios from 'axios';

export default {
  methods: {
    async fetchData() {
      // ... 代码 ...
    },

    // 将 axios 注册为方法
    axios() {
      return axios;
    }
  }
}
```

然后在模板中使用：

```html
<button @click="axios().get('/api/data')">获取数据</button>
```

### 3. 全局注册 axios

如果您在多个组件中使用 axios，可以考虑将其注册为全局属性：

```javascript
// main.js
import { createApp } from 'vue';
import axios from 'axios';
import App from './App.vue';

const app = createApp(App);
app.config.globalProperties.$axios = axios;
app.mount('#app');
```

然后在组件中通过 this.$axios 访问：

```javascript
export default {
  methods: {
    async fetchData() {
      try {
        const response = await this.$axios.get('/api/data');
        this.data = response.data;
      } catch (error) {
        console.error('获取数据失败', error);
      }
    }
  }
}
```

## 总结

1. **axios** 不需要在 setup 中返回，因为它只在 JavaScript 代码中使用，可以直接访问导入的模块。

2. **dayjs** 需要在 setup 中返回，因为它需要在 Vue 模板中使用，而模板只能访问特定的内容。

3. 这是 Vue 的设计决定的，不是 axios 或 dayjs 的特殊性质。任何需要在模板中使用的函数或变量，都需要通过 setup 返回、data 定义、methods 定义或 computed 定义。

4. 如果您只在 JavaScript 代码中使用 dayjs（如方法内部），那么不需要在 setup 中返回它。

这就是为什么 axios 不需要注册到 setup 就可以直接使用，而 dayjs 需要注册到 setup 才能在模板中使用的原因。
