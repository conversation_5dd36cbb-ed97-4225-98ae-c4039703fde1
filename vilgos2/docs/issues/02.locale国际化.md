# Element Plus 国际化 (`locale`)

## 问题背景

Element Plus 组件（如日期选择器 `el-date-picker`）默认使用英文界面。即使操作系统或浏览器设置为中文，这些组件内部的文本（如月份、星期、按钮文字）可能仍然显示为英文。

## 解决方案：设置 `locale`

为了解决这个问题，Element Plus 提供了国际化支持，通过引入和应用语言包来实现。要将组件切换为中文显示，你需要使用官方提供的中文语言包 (`zhCn`)。

主要有两种配置方式：

### 1. 全局配置 (推荐)

这是最常用且推荐的方式，特别适用于整个应用程序都需要统一使用中文的场景。

**配置位置：** 项目入口文件（通常是 `src/main.js`）。

**示例代码：**

```javascript
// src/main.js
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css' // 引入 Element Plus 基础样式
import zhCn from 'element-plus/dist/locale/zh-cn.mjs' // 引入中文语言包
import App from './App.vue'

const app = createApp(App)

// 在 app.use(ElementPlus, ...) 时传入 locale 配置
app.use(ElementPlus, { locale: zhCn })

app.mount('#app')
```

**优点：**

*   **一处配置，全局生效：** 无需在每个组件中单独处理。
*   **代码简洁：** 保持组件代码的干净。

### 2. 组件级别配置 (使用 `<el-config-provider>`)

如果你只需要在应用程序的特定部分使用中文，或者需要在一个全局配置为其他语言的应用中局部使用中文，可以使用 `<el-config-provider>` 组件。

**配置位置：** 在需要应用特定语言的 Vue 组件模板中。

**示例代码：**

```vue
<template>
  <el-config-provider :locale="zhCn">
    <!-- 这个区域内的 Element Plus 组件将显示中文 -->
    <el-date-picker v-model="dateValue" type="daterange" />
    <el-pagination :total="100" />
    <!-- ... 其他需要中文的组件 -->
  </el-config-provider>

  <!-- 这个区域外的组件将遵循全局 locale 设置或默认英文 -->
  <el-button>Button</el-button>
</template>

<script setup>
import { ref } from 'vue'
// 需要显式引入 ElConfigProvider 组件和中文语言包
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

const dateValue = ref('')
</script>
```

**优点：**

*   **灵活性高：** 允许在不同组件或页面区域使用不同的语言设置。

**缺点：**

*   **代码冗余：** 需要在每个需要的地方导入 `ElConfigProvider` 和语言包，并用其包裹相关组件。

## 总结

要解决 Element Plus 组件（如日期选择器）显示英文的问题，核心是引入并应用中文语言包 (`zhCn`)。

*   对于整个应用都需要中文的情况，**全局配置**是最优选择。
*   对于局部需要中文或覆盖全局设置的情况，使用**`<el-config-provider>`**。

确保你已经通过 npm 或 yarn 安装了 `element-plus`。
