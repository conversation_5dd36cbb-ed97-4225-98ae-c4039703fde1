# Ant Design Vue 响应式栅格系统与布局调整

Ant Design Vue 提供了强大的响应式栅格系统，允许根据不同的屏幕尺寸调整布局。

**响应式栅格属性**:

-   `:xs="24"`：在超小屏幕（<576px）时占满宽度 (24 列)
-   `:sm="24"`：在小屏幕（≥576px）时占满宽度 (24 列)
-   `:md="12"`：在中等屏幕（≥768px）时占 12 列
-   `:lg="8"`：在大屏幕（≥992px）时占 8 列
-   `:xl="6"`：在超大屏幕（≥1200px）时占 6 列

*(注意：上述列数仅为示例，实际值应根据布局需求调整)*

**示例应用 (以搜索表单为例)**:

假设我们有以下布局需求：

-   设备号: 在大屏占 4 列，中屏 8 列，小屏 24 列
-   故障模式: 在大屏占 5 列，中屏 8 列，小屏 24 列
-   日期范围: 在大屏占 5 列，中屏 8 列，小屏 24 列
-   按钮区域: 在大屏占 4 列，中屏 24 列，小屏 24 列

```vue
<a-row :gutter="[16, 16]"> // 设置行列间距
  <a-col :xs="24" :sm="24" :md="8" :lg="4" :xl="4">
    <a-form-item label="设备号：" name="equipmentNo">
      <!-- Input -->
    </a-form-item>
  </a-col>
  <a-col :xs="24" :sm="24" :md="8" :lg="5" :xl="5">
    <a-form-item label="设备故障模式：" name="stopReasonName">
      <!-- Select -->
    </a-form-item>
  </a-col>
  <a-col :xs="24" :sm="24" :md="8" :lg="5" :xl="5">
    <a-form-item label="日期范围：" name="vDay">
      <!-- RangePicker -->
    </a-form-item>
  </a-col>
  <a-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4" class="search-buttons-col">
    <!-- Buttons -->
  </a-col>
</a-row>
```

**CSS 样式调整**:

为了在小屏幕上获得更好的显示效果，可能需要添加额外的 CSS：

```css
/* 在小屏幕 (<768px) 时为按钮区域添加上边距 */
@media (max-width: 767px) {
  .search-buttons-col {
    margin-top: 16px; /* 或根据需要调整 */
    text-align: left; /* 或根据需要调整对齐方式 */
  }
  /* 可以让按钮占满宽度并右对齐 */
  .search-buttons-col .ant-btn {
     /* width: 100%; */ /* 如果需要按钮占满 */
     /* float: right; */ /* 如果需要按钮右对齐 */
  }
}

/* 保证表单项在换行时有底部间距 */
.ant-form-item {
  margin-bottom: 16px; /* 调整默认间距或按需设置 */
}
```

**布局行为总结**:

-   **大屏幕**: 保持紧凑的水平布局。
-   **中等屏幕**: 列宽调整，可能部分换行。
-   **小屏幕**: 每个表单项和按钮区域都独占一行，垂直排列。

**关于 `:gutter`**:

`:gutter` 属性用于设置栅格列之间的水平和垂直间距，单位是像素。例如 `:gutter="[16, 16]"` 表示水平间距 16px，垂直间距 16px。可以根据设计需求自由调整，不一定非要使用 24。
