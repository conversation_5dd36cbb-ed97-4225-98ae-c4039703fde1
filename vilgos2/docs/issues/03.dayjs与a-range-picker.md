# Ant Design Vue `a-range-picker` 与 `dayjs` 对象

Ant Design Vue 的 `a-range-picker` 组件在处理日期值时，期望接收 `dayjs` 对象数组，而不是格式化后的日期字符串。

**问题原因**:

当 `a-range-picker` 接收到字符串格式的日期时，它无法调用 `dayjs` 对象特有的方法（如 `isAfter`, `isBefore`, `format` 等），从而导致类似 `isAfter is not a function` 的错误。

**正确的方式**:

在 Vue 组件的状态（如 `searchForm.vDay`）中，应该存储 `dayjs` 对象。

```javascript
import dayjs from 'dayjs';

// data 或 ref/reactive 中
searchForm: {
  // ... 其他字段
  vDay: [
    dayjs().subtract(3, 'month'),  // 直接存储 dayjs 对象
    dayjs()                        // 直接存储 dayjs 对象
  ]
}
```

**错误的方式**:

在状态中存储格式化后的字符串。

```javascript
// 错误示例
searchForm: {
  // ... 其他字段
  vDay: [
    dayjs().subtract(3, 'month').format('YYYY-MM-DD'), // 存储了字符串
    dayjs().format('YYYY-MM-DD')                       // 存储了字符串
  ]
}
```

**数据流转说明**:

1.  **组件状态 (`vDay`)**: 始终保持为 `[dayjs对象, dayjs对象]`。
2.  **`a-range-picker`**: 直接绑定 `vDay`，组件内部使用 `dayjs` 对象进行日期计算和展示。
3.  **发送到后端**: 在构建请求参数时，才将 `dayjs` 对象格式化为所需的字符串格式。

    ```javascript
    // 发送请求时
    const params = {
      // ... 其他参数
      startDate: searchForm.vDay[0].format('YYYY-MM-DD'), // 格式化为字符串
      endDate: searchForm.vDay[1].format('YYYY-MM-DD')   // 格式化为字符串
    };
    // 发送 axios 请求...
    ```

**总结**:

-   在前端状态管理中，为 `a-range-picker` 绑定的值应始终是 `dayjs` 对象数组。
-   仅在需要**显示特定格式**或**发送给后端**时，才调用 `.format()` 方法将 `dayjs` 对象转换为字符串。
-   这样可以确保 `a-range-picker` 的所有内置功能（如日期比较、禁用逻辑等）都能正常工作。
