# dayjs 在 Vue 中的注册与使用

## 问题背景

在 Vue 3 项目中，当我们需要在模板中使用 dayjs 库时，可能会遇到以下错误：

```
Uncaught (in promise) TypeError: _ctx.dayjs is not a function
```

这个错误通常发生在尝试在模板中直接使用 dayjs 函数时，例如：

```html
<a-range-picker
  :value="[dayjs(searchForm.startDate), dayjs(searchForm.endDate)]"
  @change="handleDateChange"
  placeholder="请选择日期范围"
  style="width: 100%"
/>
```

## 原因分析

在 Vue 3 中，通过 `import` 语句导入的模块（如 dayjs）默认情况下**不会**自动在模板上下文中可用。这是因为 Vue 的模板编译系统只会将特定的内容暴露给模板：

1. 在 `setup()` 函数中返回的变量
2. 在 `data()` 中定义的响应式数据
3. 在 `methods` 中定义的方法
4. 在 `computed` 中定义的计算属性

## 解决方案

### 方案一：在 setup 函数中返回 dayjs（推荐）

在 setup 函数中返回 dayjs，使其在模板中可用：

```javascript
import dayjs from 'dayjs';

export default {
  setup() {
    // ... 其他代码 ...

    return {
      // ... 其他返回值 ...
      dayjs, // 将 dayjs 暴露给模板
    };
  },

  // ... 其他代码 ...
};
```

然后在模板中可以直接使用：

```html
<a-range-picker
  :value="[dayjs(searchForm.startDate), dayjs(searchForm.endDate)]"
  @change="handleDateChange"
  placeholder="请选择日期范围"
  style="width: 100%"
/>
```

### 方案二：使用全局属性

在应用的入口文件（通常是 main.js）中注册 dayjs 为全局属性：

```javascript
// main.js
import { createApp } from 'vue';
import dayjs from 'dayjs';
import App from './App.vue';

const app = createApp(App);
app.config.globalProperties.$dayjs = dayjs;
app.mount('#app');
```

然后在组件中通过 this.$dayjs 访问：

```html
<a-range-picker
  :value="[$dayjs(searchForm.startDate), $dayjs(searchForm.endDate)]"
  @change="handleDateChange"
  placeholder="请选择日期范围"
  style="width: 100%"
/>
```

### 方案三：使用 provide/inject

在应用的根组件中使用 provide 提供 dayjs：

```javascript
// App.vue
import { provide } from 'vue';
import dayjs from 'dayjs';

export default {
  setup() {
    provide('dayjs', dayjs);
    // ... 其他代码
  }
}
```

然后在子组件中使用 inject 注入：

```javascript
// 子组件
import { inject } from 'vue';

export default {
  setup() {
    const dayjs = inject('dayjs');
    // ... 其他代码
    return {
      dayjs,
      // ... 其他返回值
    };
  }
}
```

### 方案四：使用插件

创建一个 Vue 插件来注册 dayjs：

```javascript
// plugins/dayjs.js
import dayjs from 'dayjs';

export default {
  install(app) {
    app.config.globalProperties.$dayjs = dayjs;
  }
}
```

然后在入口文件中使用：

```javascript
// main.js
import { createApp } from 'vue';
import App from './App.vue';
import dayjsPlugin from './plugins/dayjs';

const app = createApp(App);
app.use(dayjsPlugin);
app.mount('#app');
```

## 最佳实践

考虑到代码结构和可维护性，推荐使用**方案一**或**方案二**：

1. **方案一**（在 setup 函数中返回 dayjs）：
   - 优点：简单直接，不需要修改全局配置
   - 缺点：需要在每个组件中单独返回 dayjs

2. **方案二**（全局属性）：
   - 优点：只需配置一次，所有组件都可以使用
   - 缺点：使用 `$` 前缀可能与其他库冲突

## 注意事项

1. 在 `data()` 方法中，`this` 指向的是组件实例，而不是 setup 函数返回的对象。因此，在 `data()` 方法中，您无法直接访问 setup 函数返回的 dayjs。

2. 如果需要在 `data()` 方法中使用 dayjs，可以直接导入：
   ```javascript
   import dayjs from 'dayjs';

   export default {
     data() {
       return {
         searchForm: {
           startDate: dayjs().subtract(3, 'month').format('YYYY-MM-DD'),
           endDate: dayjs().format('YYYY-MM-DD')
         }
       };
     }
   };
   ```

3. 确保 dayjs 库已正确安装：
   ```bash
   npm install dayjs
   # 或
   yarn add dayjs
   ```

## 总结

在 Vue 3 中使用 dayjs 时，需要确保它在模板上下文中可用。最简单的方法是在 setup 函数中返回 dayjs，或者将其注册为全局属性。选择哪种方法取决于您的项目结构和需求。
