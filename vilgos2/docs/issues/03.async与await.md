# JavaScript `async` 与 `await`

`async` 和 `await` 是 JavaScript 中用于处理异步操作的关键字，它们通常一起使用，以简化基于 Promise 的代码。

### `async` 关键字

-   **定义**：`async` 用于声明一个异步函数。异步函数会隐式地返回一个 Promise 对象。
-   **用途**：在函数前加上 `async` 关键字，表示该函数内部可能会有异步操作，并且可以使用 `await` 关键字。

### `await` 关键字

-   **定义**：`await` 用于暂停异步函数的执行，等待一个 Promise 对象的解决（resolve）或拒绝（reject）。
-   **用途**：`await` 只能在 `async` 函数内部使用。它会暂停函数的执行，直到 Promise 完成，并返回结果。

### 区别与用法

1.  **`async` 用于声明函数**：
    -   任何使用 `async` 声明的函数都会返回一个 Promise。
    -   即使函数内部没有显式返回 Promise，`async` 函数也会自动将返回值包装在一个已解决的 Promise 中。

    ```javascript
    async function fetchData() {
      return '数据已获取';
    }

    fetchData().then(data => console.log(data)); // 输出: 数据已获取
    ```

2.  **`await` 用于处理 Promise**：
    -   `await` 关键字会暂停异步函数的执行，直到 Promise 完成。
    -   如果 Promise 被解决，`await` 表达式会返回解决的值。
    -   如果 Promise 被拒绝，`await` 会抛出拒绝的值。

    ```javascript
    async function fetchData() {
      try {
        let response = await fetch('https://api.example.com/data');
        let data = await response.json();
        console.log(data);
      } catch (error) {
        console.error('请求失败:', error);
      }
    }

    fetchData();
    ```

### 总结

-   `async` 用于声明一个异步函数，使其返回一个 Promise。
-   `await` 用于暂停异步函数的执行，等待 Promise 完成，并返回其结果。
-   `await` 只能在 `async` 函数内部使用。

通过使用 `async` 和 `await`，可以使异步代码看起来更像同步代码，从而提高代码的可读性和可维护性。
