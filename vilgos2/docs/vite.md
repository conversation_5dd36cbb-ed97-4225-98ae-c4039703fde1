# src/views/ 组件规范

本目录存放应用的主要页面级 Vue 组件。为了实现路由的自动生成和维护代码的清晰性，请遵循以下规范：

## 1. 目录结构

所有页面组件必须遵循两级目录结构：

```
src/views/
├── 模块名1/             <-- 第一级：功能模块名 (全小写)
│   ├── 组件名A/         <-- 第二级：具体组件名 (全小写)
│   │   ├── index.vue    <-- 组件入口文件 (必须是 index.vue)
│   │   ├── components/  <-- (可选) 该组件私有的子组件目录
│   │   │   └── ChildA.vue
│   │   ├── css/         <-- (可选) 样式文件目录
│   │   │   └── index.css
│   │   ├── js/          <-- (可选) 外部脚本目录
│   │   │   └── index.js
│   │   └── img/         <-- (可选) 图片资源目录
│   │       └── logo.png
│   └── 组件名B/
│       └── index.vue
└── 模块名2/
    └── 组件名C/
        └── index.vue
```

*   **模块名 (第一级):** 代表一个相关功能的集合，例如 `element` (使用 Element Plus 的组件), `device` (设备相关页面), `user` (用户管理)。**必须使用全小写**，可以用连字符 `-` 分隔单词 (例如 `ant-design-vue`)。
*   **组件名 (第二级):** 代表模块下的一个具体页面或功能单元，例如 `demo1`, `list`, `edit`。**必须使用全小写**。
*   **入口文件:** 每个组件的核心 Vue 文件**必须命名为 `index.vue`** 并直接放在 `组件名` 目录下。

## 2. 自动路由生成

项目配置的 `src/router/index.js` 会自动扫描 `src/views/*/*/index.vue` 结构并生成路由。

*   **URL 路径:** 根据目录结构自动生成，格式为 `/模块名/组件名`。例如，`src/views/device/list/index.vue` 对应的访问路径是 `/device/list`。
*   **路由名称 (内部):** 自动生成，格式为 `模块名组件名` (PascalCase)，例如 `DeviceList`。主要用于编程式导航 (`router.push({ name: '...' })`)。
*   **显示标题 (UI):** 自动生成，存储在路由的 `meta.title` 字段中，**直接使用小写的 `组件名`**。例如，`src/views/device/list/index.vue` 对应的 `meta.title` 是 `list`。导航菜单等 UI 组件应优先读取 `meta.title` 来显示链接文本。

## 3. 关联文件 (CSS, JS, 图片等)

*   建议将组件相关的 CSS, JS (如果逻辑复杂需要拆分), 图片等资源文件分别放在 `组件名` 目录下的 `css/`, `js/`, `img/` 等子目录中。
*   在 `index.vue` 文件中引用这些资源时，**必须使用相对路径**。
    *   例如：`<style scoped src="./css/index.css"></style>`
    *   例如：`<script src="./js/index.js" type="module"></script>`
    *   例如：`<img src="./img/logo.png" alt="Logo">`

## 4. 子组件

*   如果一个组件包含仅供其自身使用的子组件，建议将这些子组件放在该组件目录下的 `components/` 子目录中。
    *   例如：`src/views/device/list/components/ListItem.vue`。
*   在父组件 (`index.vue` 或其关联的 `js` 文件) 中导入和注册子组件时，**必须使用相对路径**。
    *   例如 (在 `src/views/device/list/index.vue` 或 `./js/index.js` 中):
        ```javascript
        import ListItem from './components/ListItem.vue';
        // ... 在 components 选项或 setup 中使用 ListItem
        ```

遵循这些规范有助于保持项目结构的一致性，并充分利用自动路由生成带来的便利。
