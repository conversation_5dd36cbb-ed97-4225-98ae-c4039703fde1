<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourpackage.YourMapperInterface"> <!-- 请替换为你的 Mapper 接口路径 -->

    <select id="queryMonthlyDowntimeByPlName" resultType="java.util.Map">
        WITH params AS (
            -- 参数由 MyBatis Plus 动态传入
            SELECT
                #{plNames, jdbcType=VARCHAR} AS plNames_param, -- 逗号分隔的厂处名称, 或 NULL/空字符串
                #{year, jdbcType=VARCHAR} AS year_param       -- 查询年份, 如 '2024'
            FROM DUAL
        ), rangeMonths AS (
            -- 生成指定年份的所有月份
            SELECT ADD_MONTHS(TO_DATE(p.year_param || '-01-01', 'YYYY-MM-DD'), LEVEL - 1) AS months
            FROM DUAL
            CROSS JOIN params p -- 获取年份参数
            CONNECT BY LEVEL <= 12
        ), plnoMapping AS (
            -- 厂处，映射表
            SELECT PLNO
               , DECODE(PLNAME,
                   '冷轧厂酸平机组', '冷轧厂',
                   '冷轧厂热镀机组', '冷轧厂',
                   DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                          1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                          PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg'
            AND status = 'A'
        ), records AS (
            -- 事故(故障)记录
            SELECT plNo
                , TO_DATE(informDate, 'YYYYMMDD') AS informDate
                , bkReason
                , stopReason
                , totalBreakTime
            FROM DB.TBmhFA
            WHERE compID = 'sxjg'
        ), stat AS (
            -- 基础数据：应用年份和厂处筛选
            SELECT records.plNo
                , pl.PLNAME
                , records.informDate
                , records.totalBreakTime
            FROM records
            CROSS JOIN params p -- 引入参数
            LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
            WHERE 1=1
            AND pl.PLNAME IS NOT NULL
            -- 年份筛选
            AND TRUNC(records.informDate, 'YEAR') = TO_DATE(p.year_param || '-01-01', 'YYYY-MM-DD')
            -- 厂处筛选 (精确匹配列表)
            AND (p.plNames_param IS NULL OR p.plNames_param = '' OR -- 处理空字符串也视为不筛选
                 pl.PLNAME IN (
                     SELECT REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL)
                     FROM dual
                     CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                 )
            )
        ), uniquePlNames AS (
            -- 提取所有在 stat 中出现过的唯一 plName
            SELECT DISTINCT PLNAME
            FROM stat
        ), allPlNameMonths AS (
            -- 创建所有 plName 和 所有月份的组合
            SELECT
                upn.PLNAME,
                rm.months
            FROM uniquePlNames upn
            CROSS JOIN rangeMonths rm
        ), result_aggregated AS (
            -- 按 plName 和 月份 聚合实际数据
            SELECT
                PLNAME AS plName
                , TRUNC(informDate, 'MONTH') AS informDate
                , SUM(totalBreakTime) AS totalBreakTime
                , COUNT(*) AS count
            FROM stat
            GROUP BY PLNAME, TRUNC(informDate, 'MONTH')
        ), monthly_data AS (
            -- 最终的月度数据，包含所有月份（缺失月份补0）
            SELECT
                apnm.PLNAME AS plName
                , apnm.months AS informDate
                , NVL(ra.totalBreakTime, 0) AS totalBreakTime
            FROM allPlNameMonths apnm
            LEFT JOIN result_aggregated ra ON apnm.PLNAME = ra.plName AND apnm.months = ra.informDate
        ), combined_data AS ( -- 将 individual 和 total 行合并，并包含 sort_order
            SELECT
                plName AS "plName",
                LISTAGG(totalBreakTime, ',') WITHIN GROUP (ORDER BY informDate) AS "totalBreakTimes",
                1 AS sort_order -- 用于排序，确保合计行在最后
            FROM monthly_data
            GROUP BY plName
            UNION ALL
            SELECT
                '合计' AS "plName", -- 修改别名以匹配
                LISTAGG(monthly_sum, ',') WITHIN GROUP (ORDER BY informDate) AS "totalBreakTimes",
                2 AS sort_order -- 用于排序，确保合计行在最后
            FROM (
                -- 计算每个月的总故障时间
                SELECT
                    informDate,
                    SUM(totalBreakTime) AS monthly_sum
                FROM monthly_data
                GROUP BY informDate
            ) monthly_totals
        )
        -- 最外层查询，先排序，再选择最终需要的列
        SELECT "plName", '故障时间' AS "mbmc", "totalBreakTimes"
        FROM combined_data
        ORDER BY sort_order, "plName"
    </select>

</mapper>
