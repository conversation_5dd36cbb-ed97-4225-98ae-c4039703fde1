# SQL 错误修正: 无效标识符 relegationName

本文件记录了修正一个 SQL 查询中 `relegationName` 标识符无效错误的讨论过程和最终的正确查询。

## 问题描述

用户提供了一个查询事故记录的 SQL，但在 `WHERE` 子句中遇到了 `relegationName` 标识符无效的错误。

**原始错误代码片段:**

```sql
...
FROM records
LEFT JOIN relegationMapping re ON records.relegation = re.relegation
LEFT JOIN equipmentMapping eq ON records.equipmentNo = eq.equipmentNo
LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
WHERE 1=1
AND pl.PLNAME IS NOT NULL
AND records.relegationName != ' ' -- <<<< 错误发生在此处
AND (INSTR(pl.plNo, 'Y2') > 0 OR INSTR(pl.plNo, 'W2') > 0 OR INSTR(pl.plNo, 'W3') > 0)
...
```

**错误原因:** `relegationName` 列实际上来自于 `relegationMapping` CTE，该 CTE 在 `JOIN` 时被赋予了别名 `re`。因此，在 `WHERE` 子句中引用该列时，应使用别名 `re` 而不是 `records`。

## 修正后的 SQL

**最终 SQL:**

```sql
WITH relegationMapping AS (
    -- 事故归属类型，映射表
    SELECT ITEMA as relegation
         , DataA as relegationName
    FROM DB.TBmzRD
    WHERE compID = 'sxjg'
    AND nodeNo = '40310421'
    AND status = 'Y'
), equipmentMapping AS (
    -- 设备名称，映射表
    SELECT equipmentNo
        , equipmentName
    FROM DB.TBME02
    WHERE compID = 'sxjg'
), plnoMapping AS (
    -- 厂处，映射表
    SELECT PLNO
       , DECODE(PLNAME,
           '冷轧厂酸平机组', '冷轧厂',
           '冷轧厂热镀机组', '冷轧厂',
           DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                  1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                  PLNAME)) AS PLNAME
    FROM DB.TBMEI1
    WHERE COMPID = 'sxjg'
    AND status = 'A'
), records AS (
    -- 事故(故障)记录
    SELECT plno
         , equipmentNo
         , breakDownNo
         , breakDownName
         , relegation
         , TO_DATE(informDate, 'YYYYMMDD') AS informDate
    FROM DB.TBmhFA
    WHERE compID = 'sxjg'
)
--厂处编号
--厂处中文名
--设备编号
--设备中文名
--事故编号
--事故中文名
--事故归属类型（停机类型）
--停机日期
SELECT records.plNo AS "plNo"
    , pl.PLNAME AS "plName"
    , records.equipmentNo AS "equipmentNo"
    , eq.equipmentName AS "equipmentName"
    , records.breakDownNo AS "breakDownNo"
    , records.breakDownName AS "breakDownName"
    , re.relegationName AS "relegationName"
    , records.informDate AS "informDate"
FROM records
LEFT JOIN relegationMapping re ON records.relegation = re.relegation
LEFT JOIN equipmentMapping eq ON records.equipmentNo = eq.equipmentNo
LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
WHERE 1=1
AND pl.PLNAME IS NOT NULL
AND re.relegationName != ' ' -- *** 修改这里，使用别名 re ***
AND (INSTR(pl.plNo, 'Y2') > 0 OR INSTR(pl.plNo, 'W2') > 0 OR INSTR(pl.plNo, 'W3') > 0)
-- AND records.equipmentNo = 'W203020430'
-- AND records.breakDownNo = 'W203020430-001'
-- AND re.relegationName = '电气'
AND records.informDate BETWEEN TO_DATE('2000-01-01', 'YYYY-MM-DD') AND TO_DATE('2026-01-01', 'YYYY-MM-DD');
