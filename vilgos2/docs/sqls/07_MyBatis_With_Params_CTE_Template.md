# SQL 转 MyBatis Plus XML (保留 WITH params CTE) 提示词模板

**目标:** 将一个使用 `WITH params AS (...)` CTE 进行参数化处理的纯 SQL 查询，转换为 MyBatis Plus XML 映射文件格式。要求保留 `WITH params` CTE 结构，并通过 MyBatis Plus 动态传入参数值到该 CTE 内部。

**指令:**

请将以下提供的纯 SQL 查询（包含 `WITH params` CTE）转换为 MyBatis Plus XML `<select>` 标签的内容。

**要求:**

1.  **保留 SQL 主体结构:** 包括所有的 CTE 定义（如 `stopReasonMapping`, `records`, `stat` 等）和最终的 `SELECT` 语句。
2.  **保留 `WITH params` CTE:** `WITH params AS (...)` 结构必须保留在 SQL 查询的开头。
3.  **参数化 `params` CTE 内部:** 在 `params` CTE 的 `SELECT` 语句中，将原本硬编码的参数值替换为 MyBatis Plus 的占位符 `#{paramName, jdbcType=VARCHAR}`。确保占位符名称 (`paramName`) 与 Java 端传入的参数对象的属性名一致。
4.  **保留 `WHERE` 子句逻辑:** `WHERE` 子句中引用 `params` CTE 的逻辑（例如 `p.param_name IS NULL OR ...`, `REGEXP_LIKE`, `IN` 等）应保持不变。
5.  **移除 `<if>`, `<foreach>`:** 由于可选参数的逻辑已在 SQL 的 `WHERE` 子句中通过 `p.param_name IS NULL OR ...` 处理，因此 MyBatis XML 中**不应**再包含 `<if>` 或 `<foreach>` 等动态 SQL 标签来控制这些筛选条件。
6.  **指定 `resultType`:** 在 `<select>` 标签中设置正确的 `resultType`，指向你的 Java DTO 类或 `java.util.Map`。
7.  **指定 `id`:** 设置 `<select>` 标签的 `id`，使其与你的 Mapper 接口方法名匹配。

**待转换的 SQL (示例 - 使用最终优化版本):**
```sql
-- (粘贴你最终确认的、使用 WITH params CTE 和推荐逻辑的纯 SQL 查询)
WITH params AS (
    -- 参数值将由 MyBatis Plus 动态传入
    SELECT
        'Y2,W2,W3' AS plNos_param,           -- 示例值
        'W509010201' AS equipmentNo_param,     -- 示例值
        '质量原因,维修原因' AS stopReasonNames_param, -- 示例值
        '2000-01-01' AS startDate_param,     -- 示例值
        '2026-01-01' AS endDate_param       -- 示例值
    FROM DUAL
), stopReasonMapping AS (
    SELECT itemA AS stopReason, DataA AS stopReasonName FROM DB.TBmzRD WHERE compID = 'sxjg' AND nodeNo = '40310410' AND status = 'Y'
), bkReasonMapping AS (
    SELECT itemB AS bkReason, DataA AS bkReasonName FROM DB.TBmzRD WHERE compID = 'sxjg' AND nodeNo = '40310411' AND status = 'Y'
), plnoMapping AS (
    SELECT PLNO, PLNAME FROM DB.TBMEI1 WHERE COMPID = 'sxjg' AND status = 'A'
), records AS (
    SELECT plNo, TO_DATE(informDate, 'YYYYMMDD') AS informDate, equipmentNo, bkReason, stopReason, totalBreakTime FROM DB.TBmhFA WHERE compID = 'sxjg'
), stat AS (
    SELECT records.plNo, pl.PLNAME, records.equipmentNo, records.informDate, sr.stopReasonName, br.bkReasonName, records.totalBreakTime
    FROM records
    CROSS JOIN params p
    LEFT JOIN bkReasonMapping br ON records.bkReason = br.bkReason
    LEFT JOIN stopReasonMapping sr ON records.stopReason = sr.stopReason
    LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
    WHERE 1=1
    AND pl.PLNAME IS NOT NULL
    AND br.bkReasonName IS NOT NULL AND br.bkReasonName != ' '
    -- plNos 筛选逻辑 (使用 REGEXP_LIKE)
    AND (p.plNos_param IS NULL OR REGEXP_LIKE(pl.plNo, REPLACE(p.plNos_param, ',', '|')))
    -- equipmentNo 筛选逻辑
    AND (p.equipmentNo_param IS NULL OR records.equipmentNo = p.equipmentNo_param)
    -- 使用 IN 操作符的 stopReasonName 筛选 (忽略大小写和空格)
    AND (p.stopReasonNames_param IS NULL OR
         UPPER(TRIM(sr.stopReasonName)) IN (
             SELECT UPPER(TRIM(REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL)))
             FROM dual
             CONNECT BY REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
         )
    )
    -- 日期筛选
    AND records.informDate BETWEEN TO_DATE(p.startDate_param, 'YYYY-MM-DD') AND TO_DATE(p.endDate_param, 'YYYY-MM-DD')
) SELECT
    bkReasonName AS "bkReasonName"
  , SUM(totalBreakTime) AS "totalBreakTime"
  , TO_CHAR(ROUND(NVL(SUM(totalBreakTime) / NULLIF((SELECT SUM(totalBreakTime) FROM stat), 0), 0) * 100, 2), 'FM9999999990.00') || '%' AS "totalBreakTimeRate"
FROM stat
GROUP BY bkReasonName
ORDER BY SUM(totalBreakTime) DESC;
```

**请根据上述要求，将待转换的 SQL 转换为 MyBatis Plus XML `<select>` 标签的内容。**
