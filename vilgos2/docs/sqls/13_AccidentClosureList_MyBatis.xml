<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourmapper.AccidentClosureMapper"> <!-- 请替换为你的 Mapper 接口路径 -->

    <!-- 0401.事故闭环管理-目录界面 -->
    <select id="queryAccidentClosureList" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNos, jdbcType=VARCHAR}          AS plNos_param,
                #{equipmentNo, jdbcType=VARCHAR}    AS equipmentNo_param,
                #{breakDownNo, jdbcType=VARCHAR}    AS breakDownNo_param,
                #{relegationName, jdbcType=VARCHAR} AS relegationNames_param,
                #{startDate, jdbcType=VARCHAR}      AS startDate_param,
                #{endDate, jdbcType=VARCHAR}        AS endDate_param
            FROM DUAL
        ),
        relegationMapping AS (
            -- 事故归属类型，映射表
            SELECT ITEMA as relegation, DataA as relegationName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310421' AND status = 'Y'
        ),
        equipmentMapping AS (
            -- 设备名称，映射表
            SELECT equipmentNo, equipmentName
            FROM DB.TBME02
            WHERE compID = 'sxjg'
        ),
        plnoMapping AS (
            -- 厂处，映射表
            SELECT PLNO, PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
        ),
        records AS (
            -- 事故(故障)记录
            SELECT plno, equipmentNo, breakDownNo, breakDownName, relegation, TO_DATE(informDate, 'YYYYMMDD') AS informDate
            FROM DB.TBmhFA
            WHERE compID = 'sxjg'
        )
        SELECT records.plNo AS "plNo",
               pl.PLNAME AS "plName",
               records.equipmentNo AS "equipmentNo",
               eq.equipmentName AS "equipmentName",
               records.breakDownNo AS "breakDownNo",
               records.breakDownName AS "breakDownName",
               re.relegationName AS "relegationName",
               records.informDate AS "informDate"
        FROM records
        CROSS JOIN params p
        LEFT JOIN relegationMapping re ON records.relegation = re.relegation
        LEFT JOIN equipmentMapping eq ON records.equipmentNo = eq.equipmentNo
        LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
        WHERE 1=1
          AND pl.PLNAME IS NOT NULL
          AND re.relegationName IS NOT NULL AND re.relegationName != ' ' -- 确保关联到的 relegationName 有效
          -- plNos 筛选逻辑 (前缀/子串匹配)
          AND (p.plNos_param IS NULL OR p.plNos_param = '' OR REGEXP_LIKE(pl.plNo, REPLACE(p.plNos_param, ',', '|')))
          -- equipmentNo 筛选逻辑 (精确匹配)
          AND (p.equipmentNo_param IS NULL OR p.equipmentNo_param = '' OR records.equipmentNo = p.equipmentNo_param)
          -- breakDownNo 筛选逻辑 (精确匹配)
          AND (p.breakDownNo_param IS NULL OR p.breakDownNo_param = '' OR records.breakDownNo = p.breakDownNo_param)
          -- relegationName 筛选逻辑 (精确匹配逗号分隔列表中的任意一个值)
          AND (p.relegationNames_param IS NULL OR p.relegationNames_param = '' OR
               re.relegationName IN (
                   SELECT TRIM(REGEXP_SUBSTR(p.relegationNames_param, '[^,]+', 1, LEVEL))
                   FROM dual
                   CONNECT BY REGEXP_SUBSTR(p.relegationNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
               )
              )
          -- 日期筛选
          AND (p.startDate_param IS NULL OR p.startDate_param = '' OR records.informDate >= TO_DATE(p.startDate_param, 'YYYY-MM-DD'))
          AND (p.endDate_param IS NULL OR p.endDate_param = '' OR records.informDate <= TO_DATE(p.endDate_param, 'YYYY-MM-DD'))
        ORDER BY records.informDate DESC, records.plNo, records.equipmentNo, records.breakDownNo -- 添加默认排序
    </select>

</mapper>
