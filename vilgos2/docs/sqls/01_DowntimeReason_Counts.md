# 按停机原因统计次数

本文件记录了生成按停机原因统计次数的月度明细表和饼图数据的 SQL 查询及相关讨论。

## 需求 1: 月度次数明细表

**问题:** 生成一个按停机类型（reasonName）分类的年度统计表，包含各个月份的发生次数、年累计次数、占比（格式如 xx.xx%），以及最后的小计行（占比为 /）。

**最终 SQL (以 2024 年为例):**

```sql
-- 按停机原因统计次数 - 月度明细表 (xx.xx%)
WITH reasonNoMapping AS (
    -- 停机类型 (包含所有可能的停机原因及其编号)
    SELECT itemA as reasonNo,
           DataA as reasonName
    FROM DB.TBmzRD
    WHERE compID = 'sxjg'
    AND nodeNo = '40310417'
    AND status = 'Y'
    AND DataA IS NOT NULL AND DataA != ' ' -- 确保原因名称有效
),
records AS (
    -- 产线停机纪录 (只选需要的列)
    SELECT reasonNo,
           TO_DATE(startDate, 'YYYYMMDD') AS informDate
    FROM DB.TBmhS1
    WHERE compID = 'sxjg'
),
target_year_date AS (
    -- 定义目标年份
    SELECT TO_DATE('2024-01-01', 'YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
),
monthly_counts AS (
    -- 按停机原因和月份聚合次数
    SELECT
        rnm.reasonName,
        TRUNC(r.informDate, 'MONTH') AS month_date,
        COUNT(*) AS monthly_count -- 计算次数
    FROM records r
    JOIN target_year_date tyd ON TRUNC(r.informDate, 'YEAR') = tyd.year_start -- 按年过滤
    JOIN reasonNoMapping rnm ON r.reasonNo = rnm.reasonNo -- 连接原因映射
    GROUP BY rnm.reasonName, TRUNC(r.informDate, 'MONTH')
),
all_reasonNames AS (
    -- 所有有效的停机原因名称
    SELECT DISTINCT reasonName FROM reasonNoMapping
),
all_months AS (
    -- 目标年份的所有月份
    SELECT ADD_MONTHS((SELECT year_start FROM target_year_date), LEVEL - 1) AS month_date
    FROM DUAL
    CONNECT BY LEVEL <= 12
),
all_slots AS (
    -- 创建所有 原因-月份 的组合槽位
    SELECT
        arn.reasonName,
        am.month_date
    FROM all_reasonNames arn
    CROSS JOIN all_months am
),
monthly_data_full AS (
    -- 将实际月度计数左连接到所有槽位，缺失补0
    SELECT
        als.reasonName,
        als.month_date,
        NVL(mc.monthly_count, 0) AS monthly_count
    FROM all_slots als
    LEFT JOIN monthly_counts mc ON als.reasonName = mc.reasonName AND als.month_date = mc.month_date
),
pivoted_data AS (
    -- 使用条件聚合将月份数据透视到列
    SELECT
        reasonName AS "类型",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_count ELSE 0 END) AS "1月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_count ELSE 0 END) AS "2月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_count ELSE 0 END) AS "3月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_count ELSE 0 END) AS "4月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_count ELSE 0 END) AS "5月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_count ELSE 0 END) AS "6月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_count ELSE 0 END) AS "7月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_count ELSE 0 END) AS "8月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_count ELSE 0 END) AS "9月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_count ELSE 0 END) AS "10月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_count ELSE 0 END) AS "11月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_count ELSE 0 END) AS "12月",
        SUM(monthly_count) AS "年累计" -- 直接计算年累计
    FROM monthly_data_full
    GROUP BY reasonName
),
grand_total AS (
    -- 计算所有原因的总年累计次数，用于计算占比 (只计算有发生的)
    SELECT SUM("年累计") AS total_yearly_count
    FROM pivoted_data
    WHERE "年累计" > 0
),
final_data AS (
    -- 计算占比并添加排序标识
    SELECT
        p."类型", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
        CASE
            WHEN gt.total_yearly_count > 0 AND p."年累计" > 0 THEN
                 TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
            WHEN p."年累计" = 0 THEN '0.00%' -- 年累计为0显示0.00%
            ELSE '0.00%' -- 总数为0也显示0.00%
        END AS "占比",
        1 AS sort_order -- 普通行
    FROM pivoted_data p
    CROSS JOIN grand_total gt
    UNION ALL
    -- 计算小计行
    SELECT
        '小计' AS "类型",
        SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"),
        SUM("年累计"),
        '/' AS "占比", -- 小计行占比显示'/'
        2 AS sort_order -- 小计行
    FROM pivoted_data
)
-- 最终输出，按排序标识和类型排序
SELECT "类型", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
FROM final_data
ORDER BY sort_order, "类型";
```

## 需求 2: 饼图数据

**问题:** 生成用于饼图的数据，包含停机类型名称和次数占比（格式如 xx.xx%），并过滤掉占比为 0.00% 的数据。

**最终 SQL (以 2024 年为例):**

```sql
-- 按停机原因统计次数 - 饼图数据 (xx.xx%)
WITH reasonNoMapping AS (
    -- 停机类型 (包含所有可能的停机原因及其编号)
    SELECT itemA as reasonNo,
           DataA as reasonName
    FROM DB.TBmzRD
    WHERE compID = 'sxjg'
    AND nodeNo = '40310417'
    AND status = 'Y'
    AND DataA IS NOT NULL AND DataA != ' ' -- 确保原因名称有效
),
records AS (
    -- 产线停机纪录 (只选需要的列)
    SELECT reasonNo,
           TO_DATE(startDate, 'YYYYMMDD') AS informDate
    FROM DB.TBmhS1
    WHERE compID = 'sxjg'
),
target_year_date AS (
    -- 定义目标年份
    SELECT TO_DATE('2024-01-01', 'YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
),
pivoted_data AS (
    -- 计算年累计
    SELECT
        rnm.reasonName AS "类型",
        COUNT(r.reasonNo) AS "年累计" -- 直接计算年累计次数
    FROM records r
    JOIN target_year_date tyd ON TRUNC(r.informDate, 'YEAR') = tyd.year_start -- 按年过滤
    JOIN reasonNoMapping rnm ON r.reasonNo = rnm.reasonNo -- 连接原因映射
    GROUP BY rnm.reasonName
),
grand_total AS (
    -- 计算所有原因的总年累计次数 (只计算有发生的)
    SELECT SUM("年累计") AS total_yearly_count
    FROM pivoted_data
    WHERE "年累计" > 0 -- 确保总数只基于有发生的类型计算
)
-- 最终输出：类型和精确百分比字符串，过滤掉年累计为0的类型
SELECT
    p."类型",
    CASE
        WHEN gt.total_yearly_count > 0 THEN
            -- 计算百分比，保留两位小数，并格式化为 xx.xx% 字符串
            TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
        ELSE '0.00%' -- 理论上不会执行到这里，因为 total_yearly_count > 0
    END AS "占比(%)"
FROM pivoted_data p
CROSS JOIN grand_total gt
WHERE p."年累计" > 0 -- *** 在这里过滤掉年累计为0的记录 ***
ORDER BY ROUND(p."年累计" * 100 / gt.total_yearly_count, 2) DESC, p."类型"; -- 按百分比降序排序
