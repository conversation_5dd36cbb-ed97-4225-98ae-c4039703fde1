<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourmapper.EquipmentFailureMapper"> <!-- 请替换为你的 Mapper 接口路径 -->

    <!-- 0501.设备故障停机-合并后，次数 (表格) -->
    <select id="queryEquipmentFailureCountsTable" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR} AS plNames_param,
                #{year, jdbcType=VARCHAR}   AS year_param
            FROM DUAL
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, <PERSON>U<PERSON><PERSON>(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT plno, TO_DATE(startDate, 'YYYYMMDD') AS startDate
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        target_year_date AS (
             -- 使用参数或当前年份
            SELECT TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01', 'YYYY-MM-DD') AS year_start
            FROM params p
        ),
        monthly_counts AS (
            SELECT
                plm.PLNAME,
                TRUNC(r.startDate, 'MONTH') AS month_date,
                COUNT(*) AS monthly_count
            FROM records r
            CROSS JOIN params p
            JOIN target_year_date tyd ON TRUNC(r.startDate, 'YEAR') = tyd.year_start -- 按年过滤
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY plm.PLNAME, TRUNC(r.startDate, 'MONTH')
        ),
        all_plNames AS (
            -- 所有有效的厂处名称 (不过滤，保证表格结构完整)
            SELECT DISTINCT PLNAME FROM plnoMapping
        ),
        all_months AS (
            SELECT ADD_MONTHS(tyd.year_start, LEVEL - 1) AS month_date
            FROM target_year_date tyd
            CONNECT BY LEVEL <= 12
        ),
        all_slots AS (
            SELECT apn.PLNAME, am.month_date
            FROM all_plNames apn
            CROSS JOIN all_months am
        ),
        monthly_data_full AS (
            SELECT
                als.PLNAME,
                als.month_date,
                NVL(mc.monthly_count, 0) AS monthly_count
            FROM all_slots als
            LEFT JOIN monthly_counts mc ON als.PLNAME = mc.PLNAME AND als.month_date = mc.month_date
        ),
        pivoted_data AS (
            SELECT
                PLNAME AS "生产单位",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_count ELSE 0 END) AS "1月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_count ELSE 0 END) AS "2月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_count ELSE 0 END) AS "3月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_count ELSE 0 END) AS "4月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_count ELSE 0 END) AS "5月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_count ELSE 0 END) AS "6月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_count ELSE 0 END) AS "7月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_count ELSE 0 END) AS "8月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_count ELSE 0 END) AS "9月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_count ELSE 0 END) AS "10月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_count ELSE 0 END) AS "11月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_count ELSE 0 END) AS "12月",
                SUM(monthly_count) AS "年累计"
            FROM monthly_data_full
            GROUP BY PLNAME
        ),
        grand_total AS (
             -- 计算筛选后厂处的总年累计次数 (用于计算占比)
            SELECT SUM("年累计") AS total_yearly_count
            FROM pivoted_data pd
            CROSS JOIN params p
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   pd."生产单位" IN (
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
              AND pd."年累计" > 0
        ),
        final_data AS (
            SELECT
                p."生产单位", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
                CASE
                    WHEN gt.total_yearly_count > 0 AND p."年累计" > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
                    WHEN p."年累计" = 0 THEN '0.00%'
                    ELSE '0.00%'
                END AS "占比",
                1 AS sort_order
            FROM pivoted_data p
            CROSS JOIN grand_total gt
            UNION ALL
            SELECT
                '小计' AS "生产单位", SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"), SUM("年累计"), '/' AS "占比", 2 AS sort_order
            FROM pivoted_data pd
            CROSS JOIN params p
             WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   pd."生产单位" IN ( -- 确保小计也基于筛选后的厂处
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
               AND pd."年累计" > 0 -- 确保小计只包含有数据的类型
        )
        SELECT "生产单位", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
        FROM final_data
        ORDER BY sort_order, "生产单位"
    </select>

    <!-- 0501.设备故障停机-合并后，次数，图片 -->
    <select id="queryEquipmentFailureCountsChart" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR} AS plNames_param,
                #{year, jdbcType=VARCHAR}   AS year_param
            FROM DUAL
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT plno, TO_DATE(startDate,'YYYYMMDD') AS startDate
            FROM DB.TBmhS1
            WHERE compID='sxjg'
        ),
        target_year_date AS (
            SELECT TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01','YYYY-MM-DD') AS year_start
            FROM params p
        ),
        yearly_counts AS (
            SELECT plm.PLNAME, COUNT(*) AS yearly_count
            FROM records r
            CROSS JOIN params p
            JOIN target_year_date tyd ON TRUNC(r.startDate,'YEAR')=tyd.year_start
            JOIN plnoMapping plm ON r.plno=plm.PLNO
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY plm.PLNAME
        ),
        grand_total AS (
            SELECT SUM(yearly_count) AS total_yearly_count FROM yearly_counts WHERE yearly_count > 0
        )
        SELECT
            yc.PLNAME AS "生产单位",
            yc.yearly_count AS "年累计",
            CASE
                WHEN gt.total_yearly_count > 0 THEN TO_CHAR(ROUND(yc.yearly_count * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
                ELSE '0.00%'
            END AS "占比"
        FROM yearly_counts yc
        CROSS JOIN grand_total gt
        WHERE yc.yearly_count > 0
        ORDER BY ROUND(yc.yearly_count * 100 / gt.total_yearly_count, 2) DESC, yc.PLNAME
    </select>

    <!-- 0501.设备故障停机-合并后，时长 (表格) -->
    <select id="queryEquipmentFailureDurationTable" resultType="java.util.Map">
         WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR} AS plNames_param,
                #{year, jdbcType=VARCHAR}   AS year_param
            FROM DUAL
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT plno, totalTime, TO_DATE(startDate, 'YYYYMMDD') AS startDate
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        target_year_date AS (
            SELECT TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01', 'YYYY-MM-DD') AS year_start
            FROM params p
        ),
        monthly_durations AS (
            SELECT
                plm.PLNAME,
                TRUNC(r.startDate, 'MONTH') AS month_date,
                SUM(NVL(r.totalTime, 0)) AS monthly_duration
            FROM records r
            CROSS JOIN params p
            JOIN target_year_date tyd ON TRUNC(r.startDate, 'YEAR') = tyd.year_start -- 按年过滤
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY plm.PLNAME, TRUNC(r.startDate, 'MONTH')
        ),
        all_plNames AS (
            SELECT DISTINCT PLNAME FROM plnoMapping
        ),
        all_months AS (
            SELECT ADD_MONTHS(tyd.year_start, LEVEL - 1) AS month_date
            FROM target_year_date tyd
            CONNECT BY LEVEL <= 12
        ),
        all_slots AS (
            SELECT apn.PLNAME, am.month_date
            FROM all_plNames apn
            CROSS JOIN all_months am
        ),
        monthly_data_full AS (
            SELECT
                als.PLNAME,
                als.month_date,
                NVL(md.monthly_duration, 0) AS monthly_duration
            FROM all_slots als
            LEFT JOIN monthly_durations md ON als.PLNAME = md.PLNAME AND als.month_date = md.month_date
        ),
        pivoted_data AS (
            SELECT
                PLNAME AS "生产单位",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_duration ELSE 0 END) AS "1月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_duration ELSE 0 END) AS "2月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_duration ELSE 0 END) AS "3月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_duration ELSE 0 END) AS "4月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_duration ELSE 0 END) AS "5月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_duration ELSE 0 END) AS "6月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_duration ELSE 0 END) AS "7月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_duration ELSE 0 END) AS "8月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_duration ELSE 0 END) AS "9月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_duration ELSE 0 END) AS "10月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_duration ELSE 0 END) AS "11月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_duration ELSE 0 END) AS "12月",
                SUM(monthly_duration) AS "年累计"
            FROM monthly_data_full
            GROUP BY PLNAME
        ),
        grand_total AS (
            -- 计算筛选后厂处的总年累计时长 (用于计算占比)
            SELECT SUM("年累计") AS total_yearly_duration
            FROM pivoted_data pd
            CROSS JOIN params p
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   pd."生产单位" IN (
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
              AND pd."年累计" > 0
        ),
        final_data AS (
            SELECT
                p."生产单位", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
                CASE
                    WHEN gt.total_yearly_duration > 0 AND p."年累计" > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
                    WHEN p."年累计" = 0 THEN '0.00%'
                    ELSE '0.00%'
                END AS "占比",
                1 AS sort_order
            FROM pivoted_data p
            CROSS JOIN grand_total gt
            UNION ALL
            SELECT
                '小计' AS "生产单位", SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"), SUM("年累计"), '/' AS "占比", 2 AS sort_order
            FROM pivoted_data pd
            CROSS JOIN params p
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   pd."生产单位" IN ( -- 确保小计也基于筛选后的厂处
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
              AND pd."年累计" > 0 -- 确保小计只包含有数据的类型
        )
        SELECT "生产单位", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
        FROM final_data
        ORDER BY sort_order, "生产单位"
    </select>

    <!-- 0501.设备故障停机-合并后，时长，图片 -->
    <select id="queryEquipmentFailureDurationChart" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR} AS plNames_param,
                #{year, jdbcType=VARCHAR}   AS year_param
            FROM DUAL
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT plno, totalTime, TO_DATE(startDate,'YYYYMMDD') AS startDate
            FROM DB.TBmhS1
            WHERE compID='sxjg'
        ),
        target_year_date AS (
            SELECT TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01','YYYY-MM-DD') AS year_start
            FROM params p
        ),
        yearly_durations AS (
            SELECT plm.PLNAME, SUM(NVL(r.totalTime,0)) AS yearly_duration
            FROM records r
            CROSS JOIN params p
            JOIN target_year_date tyd ON TRUNC(r.startDate,'YEAR')=tyd.year_start
            JOIN plnoMapping plm ON r.plno=plm.PLNO
            WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY plm.PLNAME
        ),
        grand_total AS (
            SELECT SUM(yearly_duration) AS total_yearly_duration FROM yearly_durations WHERE yearly_duration > 0
        )
        SELECT
            yd.PLNAME AS "生产单位",
            yd.yearly_duration AS "年累计时长",
            CASE
                WHEN gt.total_yearly_duration > 0 THEN TO_CHAR(ROUND(yd.yearly_duration * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
                ELSE '0.00%'
            END AS "占比"
        FROM yearly_durations yd
        CROSS JOIN grand_total gt
        WHERE yd.yearly_duration > 0
        ORDER BY ROUND(yd.yearly_duration * 100 / gt.total_yearly_duration, 2) DESC, yd.PLNAME
    </select>

</mapper>
