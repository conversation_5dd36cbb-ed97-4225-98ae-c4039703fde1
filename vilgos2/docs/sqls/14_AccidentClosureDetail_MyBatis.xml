<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourmapper.AccidentClosureMapper"> <!-- 请替换为你的 Mapper 接口路径 -->

    <!-- 0402.事故闭环管理-事故明细 -->
    <select id="queryAccidentClosureDetail" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{breakDownNo, jdbcType=VARCHAR} AS breakDownNo_param,
                #{startDate, jdbcType=VARCHAR}   AS startDate_param,
                #{endDate, jdbcType=VARCHAR}     AS endDate_param
            FROM DUAL
        ),
        relegationMapping AS (
            -- 事故归属类型，映射表
            SELECT ITEMA as relegation, DataA as relegationName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310421' AND status = 'Y'
        ),
        breakDownMapping AS (
            -- 事故重要度等级，映射表
            SELECT ITEMA as breakDownGrade, DataA as breakDownGradeName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310401' AND status = 'Y'
        ),
        bkReasonMapping AS (
            -- 不良原因，映射表
            SELECT itemB AS bkReason, DataA AS bkReasonName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310411' AND status = 'Y'
        ),
        measureMapping AS (
            -- 整改措施，映射表
            SELECT breakDownNo, measureName, reportMemo
            FROM DB.TBMHFE
            WHERE compID = 'sxjg' AND status = 'Y'
        ),
        plnoMapping AS (
            -- 厂处，映射表
            SELECT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
        ),
        records AS (
            -- 事故(故障)记录
            SELECT plno, breakDownNo,
                   TO_DATE(informDate, 'YYYYMMDD') AS informDate,
                   TO_CHAR(TO_DATE(LPAD(informTime, 4, '0'), 'HH24MI'), 'HH24:MI') AS informTime,
                   TO_DATE(arriveDate, 'YYYYMMDD') AS arriveDate,
                   TO_CHAR(TO_DATE(LPAD(arriveTime, 4, '0'), 'HH24MI'), 'HH24:MI') AS arriveTime,
                   totalBreakTime, breakDownGrade, bkPart, bkReason, breakDownName, relegation,
                   textArea2, textArea3, textArea4
            FROM DB.TBmhFA
            WHERE compID = 'sxjg'
        )
        SELECT records.plNo AS "plNo",
               pl.PLNAME AS "plName",
               records.breakDownNo AS "breakDownNo",
               records.informDate AS "informDate",
               records.informTime AS "informTime",
               records.arriveDate AS "arriveDate",
               records.arriveTime AS "arriveTime",
               records.totalBreakTime AS "totalBreakTime",
               br.breakDownGradeName AS "breakDownGrade",
               records.bkPart AS "bkPart",
               bk.bkReasonName AS "bkReason",
               records.breakDownName AS "breakDownName",
               re.relegationName AS "relegation",
               records.textArea2 AS "textArea2",
               records.textArea3 AS "textArea3",
               records.textArea4 AS "textArea4",
               me.measureName AS "measureName",
               me.reportMemo AS "reportMemo"
        FROM records
        CROSS JOIN params p
        LEFT JOIN relegationMapping re ON records.relegation = re.relegation
        LEFT JOIN breakDownMapping br ON records.breakDownGrade = br.breakDownGrade
        LEFT JOIN bkReasonMapping bk ON records.bkReason = bk.bkReason
        LEFT JOIN plnoMapping pl ON records.plNo = pl.plNo
        LEFT JOIN measureMapping me ON records.breakDownNo = me.breakDownNo
        WHERE 1=1
          AND pl.PLNAME IS NOT NULL
          -- breakDownNo 筛选逻辑 (精确匹配 - 必需参数)
          AND records.breakDownNo = p.breakDownNo_param
          -- 日期筛选 (可选)
          AND (p.startDate_param IS NULL OR p.startDate_param = '' OR records.informDate >= TO_DATE(p.startDate_param, 'YYYY-MM-DD'))
          AND (p.endDate_param IS NULL OR p.endDate_param = '' OR records.informDate <= TO_DATE(p.endDate_param, 'YYYY-MM-DD'))
        -- 通常明细查询只返回一条或几条相关记录，排序可能不是必须，但可以加上以防万一
        ORDER BY records.informDate DESC
    </select>

</mapper>
