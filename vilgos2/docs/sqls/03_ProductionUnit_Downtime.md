# 按生产单位统计停机次数与时长

本文件记录了生成按生产单位（厂处）统计停机次数和时长的月度明细表及图表数据的 SQL 查询及相关讨论。

## 需求 1: 停机次数统计

### 1.1 月度次数明细表 (xx.xx%)

**问题:** 生成一个按生产单位（PLNAME）分类的年度统计表，包含各个月份的发生次数、年累计次数、占比（格式如 xx.xx%），以及最后的小计行（占比为 /）。

**最终 SQL (以 2024 年为例):**

```sql
-- 按生产单位统计次数 - 月度明细表 (xx.xx%)
WITH plnoMapping AS (
    -- 厂处，映射表
    SELECT DISTINCT -- 确保厂处名称唯一
           PLNO,
           DECODE(PLNAME,
               '冷轧厂酸平机组', '冷轧厂',
               '冷轧厂热镀机组', '冷轧厂',
               DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                      1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                      PLNAME)) AS PLNAME
    FROM DB.TBMEI1
    WHERE COMPID = 'sxjg'
    AND status = 'A'
    AND DECODE(PLNAME, -- 确保映射后的 PLNAME 不为空
               '冷轧厂酸平机组', '冷轧厂',
               '冷轧厂热镀机组', '冷轧厂',
               DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                      1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                      PLNAME)) IS NOT NULL
),
records AS (
    -- 产线停机纪录
    SELECT plno,
           TO_DATE(startDate, 'YYYYMMDD') AS startDate
    FROM DB.TBmhS1
    WHERE compID = 'sxjg'
),
target_year_date AS (
    -- 定义目标年份
    SELECT TO_DATE('2024-01-01', 'YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
),
monthly_counts AS (
    -- 按厂处和月份聚合次数
    SELECT
        plm.PLNAME,
        TRUNC(r.startDate, 'MONTH') AS month_date,
        COUNT(*) AS monthly_count
    FROM records r
    JOIN target_year_date tyd ON TRUNC(r.startDate, 'YEAR') = tyd.year_start -- 按年过滤
    JOIN plnoMapping plm ON r.plno = plm.PLNO -- 连接厂处映射
    GROUP BY plm.PLNAME, TRUNC(r.startDate, 'MONTH')
),
all_plNames AS (
    -- 所有有效的厂处名称
    SELECT DISTINCT PLNAME FROM plnoMapping
),
all_months AS (
    -- 目标年份的所有月份
    SELECT ADD_MONTHS((SELECT year_start FROM target_year_date), LEVEL - 1) AS month_date
    FROM DUAL
    CONNECT BY LEVEL <= 12
),
all_slots AS (
    -- 创建所有 厂处-月份 的组合槽位
    SELECT
        apn.PLNAME,
        am.month_date
    FROM all_plNames apn
    CROSS JOIN all_months am
),
monthly_data_full AS (
    -- 将实际月度计数左连接到所有槽位，缺失补0
    SELECT
        als.PLNAME,
        als.month_date,
        NVL(mc.monthly_count, 0) AS monthly_count
    FROM all_slots als
    LEFT JOIN monthly_counts mc ON als.PLNAME = mc.PLNAME AND als.month_date = mc.month_date
),
pivoted_data AS (
    -- 使用条件聚合将月份次数数据透视到列
    SELECT
        PLNAME AS "生产单位",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_count ELSE 0 END) AS "1月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_count ELSE 0 END) AS "2月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_count ELSE 0 END) AS "3月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_count ELSE 0 END) AS "4月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_count ELSE 0 END) AS "5月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_count ELSE 0 END) AS "6月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_count ELSE 0 END) AS "7月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_count ELSE 0 END) AS "8月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_count ELSE 0 END) AS "9月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_count ELSE 0 END) AS "10月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_count ELSE 0 END) AS "11月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_count ELSE 0 END) AS "12月",
        SUM(monthly_count) AS "年累计"
    FROM monthly_data_full
    GROUP BY PLNAME
),
grand_total AS (
    -- 计算所有厂处的总年累计次数
    SELECT SUM("年累计") AS total_yearly_count FROM pivoted_data WHERE "年累计" > 0
),
final_data AS (
    -- 计算占比并添加排序标识
    SELECT
        p."生产单位", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
        CASE
            WHEN gt.total_yearly_count > 0 AND p."年累计" > 0 THEN
                TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
            WHEN p."年累计" = 0 THEN '0.00%' -- 年累计为0显示0.00%
            ELSE '0.00%' -- 总数为0也显示0.00%
        END AS "占比",
        1 AS sort_order
    FROM pivoted_data p
    CROSS JOIN grand_total gt
    UNION ALL
    -- 计算小计行
    SELECT
        '小计' AS "生产单位",
        SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"),
        SUM("年累计"),
        '/' AS "占比",
        2 AS sort_order
    FROM pivoted_data
)
-- 最终输出
SELECT "生产单位", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
FROM final_data
ORDER BY sort_order, "生产单位";
```

### 1.2 次数占比饼图数据 (xx.xx%)

**问题:** 生成用于饼图的数据，包含生产单位名称和次数占比（格式如 xx.xx%），并过滤掉占比为 0.00% 的数据。

**最终 SQL (以 2024 年为例):**

```sql
-- 按生产单位统计次数 - 饼图数据 (xx.xx%)
WITH plnoMapping AS (
    SELECT DISTINCT PLNO, DECODE(PLNAME,'冷轧厂酸平机组','冷轧厂','冷轧厂热镀机组','冷轧厂',DECODE(SIGN(INSTR(PLNAME,'-')-1),1,SUBSTR(PLNAME,1,INSTR(PLNAME,'-')-1),PLNAME)) AS PLNAME FROM DB.TBMEI1 WHERE COMPID='sxjg' AND status='A' AND DECODE(PLNAME,'冷轧厂酸平机组','冷轧厂','冷轧厂热镀机组','冷轧厂',DECODE(SIGN(INSTR(PLNAME,'-')-1),1,SUBSTR(PLNAME,1,INSTR(PLNAME,'-')-1),PLNAME)) IS NOT NULL
), records AS (
    SELECT plno, TO_DATE(startDate,'YYYYMMDD') AS startDate FROM DB.TBmhS1 WHERE compID='sxjg'
), target_year_date AS (
    SELECT TO_DATE('2024-01-01','YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
), yearly_counts AS (
    SELECT plm.PLNAME, COUNT(*) AS yearly_count
    FROM records r JOIN target_year_date tyd ON TRUNC(r.startDate,'YEAR')=tyd.year_start JOIN plnoMapping plm ON r.plno=plm.PLNO
    GROUP BY plm.PLNAME
), grand_total AS (
    SELECT SUM(yearly_count) AS total_yearly_count FROM yearly_counts WHERE yearly_count > 0
)
SELECT yc.PLNAME AS "生产单位",
       CASE
           WHEN gt.total_yearly_count > 0 THEN
               TO_CHAR(ROUND(yc.yearly_count * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
           ELSE '0.00%'
       END AS "占比"
FROM yearly_counts yc CROSS JOIN grand_total gt
WHERE yc.yearly_count > 0 -- 只显示占比大于0的
ORDER BY ROUND(yc.yearly_count * 100 / gt.total_yearly_count, 2) DESC, yc.PLNAME;
```

## 需求 2: 停机时长统计

### 2.1 月度时长明细表 (xx.xx%)

**问题:** 生成一个按生产单位（PLNAME）分类的年度统计表，包含各个月份的总时长、年累计时长、占比（格式如 xx.xx%），以及最后的小计行（占比为 /）。

**最终 SQL (以 2024 年为例):**

```sql
-- 按生产单位统计时长 - 月度明细表 (xx.xx%)
WITH plnoMapping AS (
    -- 厂处，映射表
    SELECT DISTINCT -- 确保厂处名称唯一
           PLNO,
           DECODE(PLNAME,
               '冷轧厂酸平机组', '冷轧厂',
               '冷轧厂热镀机组', '冷轧厂',
               DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                      1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                      PLNAME)) AS PLNAME
    FROM DB.TBMEI1
    WHERE COMPID = 'sxjg'
    AND status = 'A'
    AND DECODE(PLNAME, -- 确保映射后的 PLNAME 不为空
               '冷轧厂酸平机组', '冷轧厂',
               '冷轧厂热镀机组', '冷轧厂',
               DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                      1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                      PLNAME)) IS NOT NULL
),
records AS (
    -- 产线停机纪录
    SELECT plno,
           totalTime, -- 包含时长
           TO_DATE(startDate, 'YYYYMMDD') AS startDate
    FROM DB.TBmhS1
    WHERE compID = 'sxjg'
),
target_year_date AS (
    -- 定义目标年份
    SELECT TO_DATE('2024-01-01', 'YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
),
monthly_durations AS (
    -- 按厂处和月份聚合时长
    SELECT
        plm.PLNAME,
        TRUNC(r.startDate, 'MONTH') AS month_date,
        SUM(NVL(r.totalTime, 0)) AS monthly_duration
    FROM records r
    JOIN target_year_date tyd ON TRUNC(r.startDate, 'YEAR') = tyd.year_start -- 按年过滤
    JOIN plnoMapping plm ON r.plno = plm.PLNO -- 连接厂处映射
    GROUP BY plm.PLNAME, TRUNC(r.startDate, 'MONTH')
),
all_plNames AS (
    -- 所有有效的厂处名称
    SELECT DISTINCT PLNAME FROM plnoMapping
),
all_months AS (
    -- 目标年份的所有月份
    SELECT ADD_MONTHS((SELECT year_start FROM target_year_date), LEVEL - 1) AS month_date
    FROM DUAL
    CONNECT BY LEVEL <= 12
),
all_slots AS (
    -- 创建所有 厂处-月份 的组合槽位
    SELECT
        apn.PLNAME,
        am.month_date
    FROM all_plNames apn
    CROSS JOIN all_months am
),
monthly_data_full AS (
    -- 将实际月度时长左连接到所有槽位，缺失补0
    SELECT
        als.PLNAME,
        als.month_date,
        NVL(md.monthly_duration, 0) AS monthly_duration
    FROM all_slots als
    LEFT JOIN monthly_durations md ON als.PLNAME = md.PLNAME AND als.month_date = md.month_date
),
pivoted_data AS (
    -- 使用条件聚合将月份时长数据透视到列
    SELECT
        PLNAME AS "生产单位",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_duration ELSE 0 END) AS "1月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_duration ELSE 0 END) AS "2月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_duration ELSE 0 END) AS "3月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_duration ELSE 0 END) AS "4月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_duration ELSE 0 END) AS "5月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_duration ELSE 0 END) AS "6月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_duration ELSE 0 END) AS "7月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_duration ELSE 0 END) AS "8月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_duration ELSE 0 END) AS "9月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_duration ELSE 0 END) AS "10月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_duration ELSE 0 END) AS "11月",
        SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_duration ELSE 0 END) AS "12月",
        SUM(monthly_duration) AS "年累计"
    FROM monthly_data_full
    GROUP BY PLNAME
),
grand_total AS (
    -- 计算所有厂处的总年累计时长
    SELECT SUM("年累计") AS total_yearly_duration FROM pivoted_data WHERE "年累计" > 0
),
final_data AS (
    -- 计算占比并添加排序标识
    SELECT
        p."生产单位", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
        CASE
            WHEN gt.total_yearly_duration > 0 AND p."年累计" > 0 THEN
                TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
            WHEN p."年累计" = 0 THEN '0.00%'
            ELSE '0.00%'
        END AS "占比",
        1 AS sort_order
    FROM pivoted_data p
    CROSS JOIN grand_total gt
    UNION ALL
    -- 计算小计行
    SELECT
        '小计' AS "生产单位",
        SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"),
        SUM("年累计"),
        '/' AS "占比",
        2 AS sort_order
    FROM pivoted_data
)
-- 最终输出
SELECT "生产单位", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
FROM final_data
ORDER BY sort_order, "生产单位";
```

### 2.2 时长占比图表数据 (xx.xx%)

**问题:** 生成用于图表的数据，包含生产单位名称和时长占比（格式如 xx.xx%），并过滤掉占比为 0.00% 的数据。

**最终 SQL (以 2024 年为例):**

```sql
-- 按生产单位统计时长 - 图表数据 (xx.xx%)
WITH plnoMapping AS (
    SELECT DISTINCT PLNO, DECODE(PLNAME,'冷轧厂酸平机组','冷轧厂','冷轧厂热镀机组','冷轧厂',DECODE(SIGN(INSTR(PLNAME,'-')-1),1,SUBSTR(PLNAME,1,INSTR(PLNAME,'-')-1),PLNAME)) AS PLNAME FROM DB.TBMEI1 WHERE COMPID='sxjg' AND status='A' AND DECODE(PLNAME,'冷轧厂酸平机组','冷轧厂','冷轧厂热镀机组','冷轧厂',DECODE(SIGN(INSTR(PLNAME,'-')-1),1,SUBSTR(PLNAME,1,INSTR(PLNAME,'-')-1),PLNAME)) IS NOT NULL
), records AS (
    SELECT plno, totalTime, TO_DATE(startDate,'YYYYMMDD') AS startDate FROM DB.TBmhS1 WHERE compID='sxjg'
), target_year_date AS (
    SELECT TO_DATE('2024-01-01','YYYY-MM-DD') AS year_start FROM DUAL -- *** 修改这里选择年份 ***
), yearly_durations AS (
    SELECT plm.PLNAME, SUM(NVL(r.totalTime,0)) AS yearly_duration
    FROM records r JOIN target_year_date tyd ON TRUNC(r.startDate,'YEAR')=tyd.year_start JOIN plnoMapping plm ON r.plno=plm.PLNO
    GROUP BY plm.PLNAME
), grand_total AS (
    SELECT SUM(yearly_duration) AS total_yearly_duration FROM yearly_durations WHERE yearly_duration > 0
)
SELECT yd.PLNAME AS "生产单位",
       CASE
           WHEN gt.total_yearly_duration > 0 THEN
               TO_CHAR(ROUND(yd.yearly_duration * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
           ELSE '0.00%'
       END AS "占比"
FROM yearly_durations yd CROSS JOIN grand_total gt
WHERE yd.yearly_duration > 0 -- 只显示占比大于0的
ORDER BY ROUND(yd.yearly_duration * 100 / gt.total_yearly_duration, 2) DESC, yd.PLNAME;
