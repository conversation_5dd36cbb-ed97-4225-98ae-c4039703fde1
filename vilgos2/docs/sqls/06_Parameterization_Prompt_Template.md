# SQL 参数化转换提示词模板

**目标:** 将包含动态筛选条件（如 MyBatis 标签或注释掉的硬编码条件）的 SQL 查询，转换为使用 Oracle SQL 的 `WITH params AS (...)` CTE 进行参数化的形式，使 SQL 脚本更独立。

**指令:**

请修改以下 SQL 查询，将 WHERE 子句中的动态筛选条件替换为使用 Oracle SQL 的 `WITH params AS (...)` CTE 来传入参数。

**要求:**

1.  在查询的开头定义一个名为 `params` 的 CTE，结构类似 `WITH params AS (SELECT '值1' AS param1, '值2' AS param2, ... FROM DUAL)`。
2.  在 `params` CTE 中定义所有需要参数化的筛选字段。如果某个字段不需要筛选，请将其对应的值设置为 `NULL`。
3.  在主查询的 `WHERE` 子句中，使用 `CROSS JOIN params p` 引入参数。
4.  对于每个参数化的筛选条件，使用 `(p.param_name IS NULL OR <实际筛选逻辑>)` 的结构，确保当参数为 `NULL` 时该条件不生效。
5.  根据参数类型和匹配需求，应用以下**特定的 SQL 实现逻辑**:

    *   **情况 1: 精确匹配单个值** (例如 `equipmentNo_param`)
        *   **逻辑:** `col = p.param_name`
        *   **实现:** `AND (p.equipmentNo_param IS NULL OR records.equipmentNo = p.equipmentNo_param)`

    *   **情况 2: 精确匹配逗号分隔列表中的任意一个值** (例如 `stopReasonNames_param`，需要忽略大小写和空格)
        *   **逻辑:** `UPPER(TRIM(col))` 在 `UPPER(TRIM(拆分后的参数列表))` 中。
        *   **实现 (使用 IN):**
            ```sql
            AND (p.stopReasonNames_param IS NULL OR
                 UPPER(TRIM(sr.stopReasonName)) IN (
                     SELECT UPPER(TRIM(REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL)))
                     FROM dual
                     CONNECT BY REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                 )
            )
            ```

    *   **情况 3: 包含匹配逗号分隔列表中的任意一个前缀/子串** (例如 `plNos_param`)
        *   **逻辑:** `col` 包含 `前缀1` 或 `前缀2` 或 ...
        *   **实现 (使用 REGEXP_LIKE):**
            ```sql
            AND (p.plNos_param IS NULL OR REGEXP_LIKE(pl.plNo, REPLACE(p.plNos_param, ',', '|')))
            ```

**待转换的 SQL:**
```sql
-- (在此处粘贴你需要转换的原始 SQL 查询)
WITH ... AS (...),
records AS (...)
SELECT ...
FROM records
-- 可能包含 MyBatis 标签或注释掉的 WHERE 条件
WHERE 1=1
-- <if test="plNos != null"> AND (INSTR(pl.plNo, #{plNo1}) > 0 OR INSTR(pl.plNo, #{plNo2}) > 0) </if>
-- AND records.equipmentNo = 'W123'
-- AND (sr.stopReasonName = '原因A' OR sr.stopReasonName = '原因B')

```

**请根据上述要求和特定实现逻辑，将待转换的 SQL 转换为使用 `WITH params` CTE 的形式。**
