<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourpackage.YourMapperInterface">    <!-- 请替换为你的 Mapper 接口路径 -->

    <select id="queryWeeklyDowntimeByPlName" resultType="java.util.Map">
        WITH params AS (
            -- 参数由 MyBatis Plus 动态传入
            SELECT
                #{month, jdbcType=VARCHAR} AS month_param,     -- 查询月份, 'YYYY-MM'
                #{plNames, jdbcType=VARCHAR} AS plNames_param  -- 可选的逗号分隔厂处名称, 或 NULL/空字符串
            FROM DUAL
        ), target_month_date AS (
            -- 定义目标月份
            SELECT TO_DATE(p.month_param || '-01', 'YYYY-MM-DD') AS month_start
            FROM params p
        ), plnoMapping_base AS (
             -- Base mapping logic, applied once
            SELECT
                   PLNO,
                   PLNAME as OriginalPLNAME,
                   DECODE(PLNAME,
                       '冷轧厂酸平机组', '冷轧厂',
                       '冷轧厂热镀机组', '冷轧厂',
                       DECODE(SIGN(INSTR(PLNAME, '-') - 1),
                              1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1),
                              PLNAME)) AS MappedPLNAME -- 映射后的名称
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg'
            AND status = 'A'
        ), plnoMapping_valid AS (
            -- Filtered mapping: only valid mapped names
            SELECT PLNO, MappedPLNAME
            FROM plnoMapping_base
            WHERE MappedPLNAME IS NOT NULL
        ), plnoMapping_unique AS (
            -- Unique mapped names for scaffold building
             SELECT DISTINCT MappedPLNAME
             FROM plnoMapping_valid
        ), records AS (
            -- 事故(故障)记录
            SELECT plNo
                , TO_DATE(informDate, 'YYYYMMDD') AS informDate
                , bkReason
                , stopReason
                , totalBreakTime
            FROM DB.TBmhFA
            WHERE compID = 'sxjg'
        ), stat_daily AS (
            -- 基础日度数据：过滤月份, 使用映射后的厂处名称
            SELECT
                plm.MappedPLNAME AS PLNAME, -- 使用映射后的 PLNAME
                r.informDate,
                r.totalBreakTime,
                tmd.month_start,
                CEIL(EXTRACT(DAY FROM r.informDate) / 7) AS week_of_month
            FROM records r
            JOIN target_month_date tmd ON TRUNC(r.informDate, 'MONTH') = tmd.month_start -- 过滤月份
            JOIN plnoMapping_valid plm ON r.plNo = plm.PLNO -- 使用有效映射表
        ), weekly_agg AS (
            -- 按厂处(映射后)、月份、周次聚合目标月份的故障时间
            SELECT
                PLNAME, -- MappedPLNAME
                month_start,
                week_of_month,
                SUM(totalBreakTime) AS weekly_total_break_time
            FROM stat_daily
            GROUP BY PLNAME, month_start, week_of_month
        ), all_weeks AS (
            -- 生成所有可能的周次 (1到5周)
            SELECT LEVEL AS week_num FROM DUAL CONNECT BY LEVEL <= 5
        ), all_slots AS (
            -- 创建目标月份的所有 唯一映射厂处(plnoMapping_unique)-周次 的组合槽位
            SELECT
                plu.MappedPLNAME AS PLNAME, -- 使用唯一映射后的 PLNAME
                tmd.month_start,
                aw.week_num,
                tmd.month_start + (aw.week_num - 1) * 7 AS week_start_date,
                LEAST(tmd.month_start + aw.week_num * 7 - 1, LAST_DAY(tmd.month_start)) AS week_end_date
            FROM plnoMapping_unique plu -- 从唯一映射厂处开始
            CROSS JOIN target_month_date tmd
            CROSS JOIN all_weeks aw
            WHERE tmd.month_start + (aw.week_num - 1) * 7 <= LAST_DAY(tmd.month_start)
        ), weekly_data_full AS (
            -- 将实际的周聚合数据(weekly_agg)左连接到所有可能的槽位(all_slots)上
            SELECT
                als.PLNAME, -- MappedPLNAME
                als.month_start,
                als.week_num,
                NVL(wa.weekly_total_break_time, 0) AS weekly_total_break_time,
                '第' || als.week_num || '周(' || TO_CHAR(als.week_start_date, 'MM.DD') || '-' || TO_CHAR(als.week_end_date, 'MM.DD') || ')' AS week_range_str
            FROM all_slots als
            LEFT JOIN weekly_agg wa
                ON als.PLNAME = wa.PLNAME -- Join on MappedPLNAME
                AND als.month_start = wa.month_start
                AND als.week_num = wa.week_of_month
        ), final_agg AS (
             -- Aggregate weekly data per MappedPLNAME
             SELECT
                wdf.PLNAME, -- MappedPLNAME
                wdf.month_start,
                LISTAGG(wdf.week_range_str, ',') WITHIN GROUP (ORDER BY wdf.week_num) AS mzrqfw,
                LISTAGG(wdf.weekly_total_break_time, ',') WITHIN GROUP (ORDER BY wdf.week_num) AS mzgzsj
             FROM weekly_data_full wdf
             GROUP BY wdf.PLNAME, wdf.month_start
        )
        -- Final Selection: Join back to get a representative PLNO and apply filtering
        SELECT
            SUBSTR((SELECT MIN(plv.PLNO) FROM plnoMapping_valid plv WHERE plv.MappedPLNAME = fa.PLNAME), 0, 2) AS "plNo",
            fa.PLNAME AS "zbgkglbm", -- This is MappedPLNAME
            '故障时间' AS "mbmc",
            fa.mzrqfw,
            fa.mzgzsj
        FROM final_agg fa
        CROSS JOIN params p -- 引入参数以便筛选
        WHERE (p.plNames_param IS NULL OR p.plNames_param = '' OR
               fa.PLNAME IN ( -- 筛选仍然基于映射后的 PLNAME
                   SELECT REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL)
                   FROM dual
                   CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
               ))
        ORDER BY fa.PLNAME
    </select>

</mapper>
