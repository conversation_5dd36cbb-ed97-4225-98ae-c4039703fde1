<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourmapper.DowntimeMapper"> <!-- 请替换为你的 Mapper 接口路径 -->

    <!-- 0301.各类停机类型占比-合并后，次数 (表格) -->
    <select id="queryDowntimeCountsByTypeTable" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR}     AS plNames_param,
                #{reasonNames, jdbcType=VARCHAR} AS reasonNames_param,
                #{year, jdbcType=VARCHAR}       AS year_param
            FROM DUAL
        ),
        reasonNoMapping AS (
            SELECT itemA as reasonNo, DataA as reasonName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310417' AND status = 'Y' AND DataA IS NOT NULL AND DataA != ' '
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT reasonNo, plno, TO_DATE(startDate, 'YYYYMMDD') AS informDate
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        filtered_records AS (
            SELECT r.reasonNo, r.informDate
            FROM records r
            CROSS JOIN params p
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.year_param IS NULL OR p.year_param = '' OR TO_CHAR(r.informDate, 'YYYY') = p.year_param) -- 年份筛选
              AND (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
        ),
        yearly_counts_all_reasons AS (
             -- 计算筛选后厂区/年份下，各原因的总次数 (用于计算占比分母)
             SELECT rnm.reasonName, COUNT(*) as yearly_count
             FROM filtered_records fr
             JOIN reasonNoMapping rnm ON fr.reasonNo = rnm.reasonNo
             GROUP BY rnm.reasonName
        ),
        monthly_counts AS (
            SELECT
                rnm.reasonName,
                TRUNC(fr.informDate, 'MONTH') AS month_date,
                COUNT(*) AS monthly_count
            FROM filtered_records fr
            CROSS JOIN params p
            JOIN reasonNoMapping rnm ON fr.reasonNo = rnm.reasonNo
            WHERE (p.reasonNames_param IS NULL OR p.reasonNames_param = '' OR
                   rnm.reasonName IN ( -- 类型筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY rnm.reasonName, TRUNC(fr.informDate, 'MONTH')
        ),
        all_reasonNames AS (
             -- 所有有效的停机原因名称 (不过滤，保证表格结构完整)
            SELECT DISTINCT reasonName FROM reasonNoMapping
        ),
        all_months AS (
            SELECT ADD_MONTHS(TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01', 'YYYY-MM-DD'), LEVEL - 1) AS month_date
            FROM params p
            CONNECT BY LEVEL <= 12
        ),
        all_slots AS (
            SELECT arn.reasonName, am.month_date
            FROM all_reasonNames arn
            CROSS JOIN all_months am
        ),
        monthly_data_full AS (
            SELECT
                als.reasonName,
                als.month_date,
                NVL(mc.monthly_count, 0) AS monthly_count
            FROM all_slots als
            LEFT JOIN monthly_counts mc ON als.reasonName = mc.reasonName AND als.month_date = mc.month_date
        ),
        pivoted_data AS (
            SELECT
                reasonName AS "类型",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_count ELSE 0 END) AS "1月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_count ELSE 0 END) AS "2月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_count ELSE 0 END) AS "3月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_count ELSE 0 END) AS "4月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_count ELSE 0 END) AS "5月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_count ELSE 0 END) AS "6月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_count ELSE 0 END) AS "7月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_count ELSE 0 END) AS "8月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_count ELSE 0 END) AS "9月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_count ELSE 0 END) AS "10月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_count ELSE 0 END) AS "11月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_count ELSE 0 END) AS "12月",
                SUM(monthly_count) AS "年累计"
            FROM monthly_data_full
            GROUP BY reasonName
        ),
        grand_total AS (
            SELECT SUM(yearly_count) AS total_yearly_count
            FROM yearly_counts_all_reasons
            WHERE yearly_count > 0
        ),
        final_data AS (
            SELECT
                p."类型", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
                CASE
                    WHEN gt.total_yearly_count > 0 AND p."年累计" > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
                    WHEN p."年累计" = 0 THEN '0.00%'
                    ELSE '0.00%'
                END AS "占比",
                1 AS sort_order
            FROM pivoted_data p
            CROSS JOIN grand_total gt
            UNION ALL
            SELECT
                '小计' AS "类型", SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"), SUM("年累计"), '/' AS "占比", 2 AS sort_order
            FROM pivoted_data
            WHERE "年累计" > 0 -- 确保小计只包含有数据的类型
        )
        SELECT "类型", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
        FROM final_data
        ORDER BY sort_order, "类型"
    </select>

    <!-- 0301.各类停机类型占比-合并后，次数，图片(类型, 年累计次数, xx.xx%) -->
    <select id="queryDowntimeCountsByTypeChart" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR}     AS plNames_param,
                #{reasonNames, jdbcType=VARCHAR} AS reasonNames_param,
                #{year, jdbcType=VARCHAR}       AS year_param
            FROM DUAL
        ),
        reasonNoMapping AS (
            SELECT itemA as reasonNo, DataA as reasonName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310417' AND status = 'Y' AND DataA IS NOT NULL AND DataA != ' '
        ),
        plnoMapping AS (
             SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT reasonNo, plno, TO_DATE(startDate, 'YYYYMMDD') AS informDate
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        pivoted_data AS (
            SELECT
                rnm.reasonName AS "类型",
                COUNT(r.reasonNo) AS "年累计"
            FROM records r
            CROSS JOIN params p
            JOIN reasonNoMapping rnm ON r.reasonNo = rnm.reasonNo
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.year_param IS NULL OR p.year_param = '' OR TO_CHAR(r.informDate, 'YYYY') = p.year_param) -- 年份筛选
              AND (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
              AND (p.reasonNames_param IS NULL OR p.reasonNames_param = '' OR
                   rnm.reasonName IN ( -- 类型筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY rnm.reasonName
        ),
        grand_total AS (
            SELECT SUM("年累计") AS total_yearly_count
            FROM pivoted_data
            WHERE "年累计" > 0
        )
        SELECT
            p."类型",
            p."年累计",
            CASE
                WHEN gt.total_yearly_count > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_count, 2), 'FM990.00') || '%'
                ELSE '0.00%'
            END AS "占比(%)"
        FROM pivoted_data p
        CROSS JOIN grand_total gt
        WHERE p."年累计" > 0
        ORDER BY ROUND(p."年累计" * 100 / gt.total_yearly_count, 2) DESC, p."类型"
    </select>

    <!-- 0301.各类停机类型占比-合并后，时长 (表格) -->
    <select id="queryDowntimeDurationByTypeTable" resultType="java.util.Map">
        WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR}     AS plNames_param,
                #{reasonNames, jdbcType=VARCHAR} AS reasonNames_param,
                #{year, jdbcType=VARCHAR}       AS year_param
            FROM DUAL
        ),
        reasonNoMapping AS (
            SELECT itemA as reasonNo, DataA as reasonName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310417' AND status = 'Y' AND DataA IS NOT NULL AND DataA != ' '
        ),
        plnoMapping AS (
            SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT reasonNo, plno, TO_DATE(startDate, 'YYYYMMDD') AS informDate, totalTime
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        filtered_records AS (
             SELECT r.reasonNo, r.informDate, r.totalTime
            FROM records r
            CROSS JOIN params p
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.year_param IS NULL OR p.year_param = '' OR TO_CHAR(r.informDate, 'YYYY') = p.year_param) -- 年份筛选
              AND (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
        ),
        yearly_durations_all_reasons AS (
             -- 计算筛选后厂区/年份下，各原因的总时长 (用于计算占比分母)
             SELECT rnm.reasonName, SUM(NVL(fr.totalTime, 0)) as yearly_duration
             FROM filtered_records fr
             JOIN reasonNoMapping rnm ON fr.reasonNo = rnm.reasonNo
             GROUP BY rnm.reasonName
        ),
        monthly_durations AS (
            SELECT
                rnm.reasonName,
                TRUNC(fr.informDate, 'MONTH') AS month_date,
                SUM(NVL(fr.totalTime, 0)) AS monthly_duration
            FROM filtered_records fr
            CROSS JOIN params p
            JOIN reasonNoMapping rnm ON fr.reasonNo = rnm.reasonNo
            WHERE (p.reasonNames_param IS NULL OR p.reasonNames_param = '' OR
                   rnm.reasonName IN ( -- 类型筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY rnm.reasonName, TRUNC(fr.informDate, 'MONTH')
        ),
        all_reasonNames AS (
            SELECT DISTINCT reasonName FROM reasonNoMapping
        ),
        all_months AS (
             SELECT ADD_MONTHS(TO_DATE(NVL(p.year_param, TO_CHAR(SYSDATE, 'YYYY')) || '-01-01', 'YYYY-MM-DD'), LEVEL - 1) AS month_date
            FROM params p
            CONNECT BY LEVEL <= 12
        ),
        all_slots AS (
            SELECT arn.reasonName, am.month_date
            FROM all_reasonNames arn
            CROSS JOIN all_months am
        ),
        monthly_data_full AS (
            SELECT
                als.reasonName,
                als.month_date,
                NVL(md.monthly_duration, 0) AS monthly_duration
            FROM all_slots als
            LEFT JOIN monthly_durations md ON als.reasonName = md.reasonName AND als.month_date = md.month_date
        ),
        pivoted_data AS (
            SELECT
                reasonName AS "分类",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 1 THEN monthly_duration ELSE 0 END) AS "1月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 2 THEN monthly_duration ELSE 0 END) AS "2月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 3 THEN monthly_duration ELSE 0 END) AS "3月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 4 THEN monthly_duration ELSE 0 END) AS "4月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 5 THEN monthly_duration ELSE 0 END) AS "5月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 6 THEN monthly_duration ELSE 0 END) AS "6月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 7 THEN monthly_duration ELSE 0 END) AS "7月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 8 THEN monthly_duration ELSE 0 END) AS "8月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 9 THEN monthly_duration ELSE 0 END) AS "9月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 10 THEN monthly_duration ELSE 0 END) AS "10月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 11 THEN monthly_duration ELSE 0 END) AS "11月",
                SUM(CASE WHEN EXTRACT(MONTH FROM month_date) = 12 THEN monthly_duration ELSE 0 END) AS "12月",
                SUM(monthly_duration) AS "年累计"
            FROM monthly_data_full
            GROUP BY reasonName
        ),
        grand_total AS (
            SELECT SUM(yearly_duration) AS total_yearly_duration
            FROM yearly_durations_all_reasons
            WHERE yearly_duration > 0
        ),
        final_data AS (
            SELECT
                p."分类", p."1月", p."2月", p."3月", p."4月", p."5月", p."6月", p."7月", p."8月", p."9月", p."10月", p."11月", p."12月", p."年累计",
                CASE
                    WHEN gt.total_yearly_duration > 0 AND p."年累计" > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
                    WHEN p."年累计" = 0 THEN '0.00%'
                    ELSE '0.00%'
                END AS "占比",
                1 AS sort_order
            FROM pivoted_data p
            CROSS JOIN grand_total gt
            UNION ALL
            SELECT
                '小计' AS "分类", SUM("1月"), SUM("2月"), SUM("3月"), SUM("4月"), SUM("5月"), SUM("6月"), SUM("7月"), SUM("8月"), SUM("9月"), SUM("10月"), SUM("11月"), SUM("12月"), SUM("年累计"), '/' AS "占比", 2 AS sort_order
            FROM pivoted_data
            WHERE "年累计" > 0 -- 确保小计只包含有数据的类型
        )
        SELECT "分类", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "年累计", "占比"
        FROM final_data
        ORDER BY sort_order, "分类"
    </select>

    <!-- 0301.各类停机类型占比-合并后，时长，图片(类型, 年累计时长, xx.xx%) -->
    <select id="queryDowntimeDurationByTypeChart" resultType="java.util.Map">
         WITH params AS (
            SELECT
                #{plNames, jdbcType=VARCHAR}     AS plNames_param,
                #{reasonNames, jdbcType=VARCHAR} AS reasonNames_param,
                #{year, jdbcType=VARCHAR}       AS year_param
            FROM DUAL
        ),
        reasonNoMapping AS (
            SELECT itemA as reasonNo, DataA as reasonName
            FROM DB.TBmzRD
            WHERE compID = 'sxjg' AND nodeNo = '40310417' AND status = 'Y' AND DataA IS NOT NULL AND DataA != ' '
        ),
        plnoMapping AS (
             SELECT DISTINCT PLNO,
                   DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                          DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) AS PLNAME
            FROM DB.TBMEI1
            WHERE COMPID = 'sxjg' AND status = 'A'
              AND DECODE(PLNAME, '冷轧厂酸平机组', '冷轧厂', '冷轧厂热镀机组', '冷轧厂',
                         DECODE(SIGN(INSTR(PLNAME, '-') - 1), 1, SUBSTR(PLNAME, 1, INSTR(PLNAME, '-') - 1), PLNAME)) IS NOT NULL
        ),
        records AS (
            SELECT reasonNo, plno, TO_DATE(startDate, 'YYYYMMDD') AS informDate, totalTime
            FROM DB.TBmhS1
            WHERE compID = 'sxjg'
        ),
        pivoted_data AS (
            SELECT
                rnm.reasonName AS "类型",
                SUM(NVL(r.totalTime, 0)) AS "年累计"
            FROM records r
            CROSS JOIN params p
            JOIN reasonNoMapping rnm ON r.reasonNo = rnm.reasonNo
            JOIN plnoMapping plm ON r.plno = plm.PLNO
            WHERE (p.year_param IS NULL OR p.year_param = '' OR TO_CHAR(r.informDate, 'YYYY') = p.year_param) -- 年份筛选
              AND (p.plNames_param IS NULL OR p.plNames_param = '' OR
                   plm.PLNAME IN ( -- 厂处筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.plNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
              AND (p.reasonNames_param IS NULL OR p.reasonNames_param = '' OR
                   rnm.reasonName IN ( -- 类型筛选 (精确匹配列表)
                       SELECT TRIM(REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL))
                       FROM dual
                       CONNECT BY REGEXP_SUBSTR(p.reasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
                   )
                  )
            GROUP BY rnm.reasonName
        ),
        grand_total AS (
            SELECT SUM("年累计") AS total_yearly_duration
            FROM pivoted_data
            WHERE "年累计" > 0
        )
        SELECT
            p."类型",
            p."年累计",
            CASE
                WHEN gt.total_yearly_duration > 0 THEN TO_CHAR(ROUND(p."年累计" * 100 / gt.total_yearly_duration, 2), 'FM990.00') || '%'
                ELSE '0.00%'
            END AS "占比(%)"
        FROM pivoted_data p
        CROSS JOIN grand_total gt
        WHERE p."年累计" > 0
        ORDER BY ROUND(p."年累计" * 100 / gt.total_yearly_duration, 2) DESC, p."类型"
    </select>

</mapper>
