# SQL 参数化解析问题总结 (plNos, equipmentNo, stopReasonNames)

本文档总结了在尝试将依赖外部框架（如 MyBatis）动态生成的 SQL 筛选条件，转换为使用 Oracle SQL 内置的 `WITH params AS (...)` CTE 和标准 SQL 逻辑进行参数化时，针对 `plNos_param`, `equipmentNo_param`, `stopReasonNames_param` 这三个参数遇到的问题及解决方案。

**目标:** 使 SQL 查询脚本更独立，不依赖外部拼接，通过在 `params` CTE 中设置参数值（包括 `NULL` 来表示不筛选）来控制查询行为。

**通用方法:** 对于可选参数，在 `WHERE` 子句中使用 `(p.param_name IS NULL OR <实际筛选逻辑>)` 的结构。如果参数在 `params` CTE 中为 `NULL`，则该筛选条件被忽略。

---

## 1. `equipmentNo_param`

*   **需求:** 精确匹配单个设备编号。
*   **逻辑:** `records.equipmentNo = p.equipmentNo_param`
*   **实现:**
    ```sql
    -- equipmentNo 筛选逻辑
    AND (p.equipmentNo_param IS NULL OR records.equipmentNo = p.equipmentNo_param)
    ```
*   **问题:** 无。这种标准方式工作正常。

---

## 2. `stopReasonNames_param`

*   **需求:** 精确匹配逗号分隔列表中的**任何一个**原因名称（例如，`'质量原因,维修原因'` 应该匹配 `sr.stopReasonName = '质量原因'` 或 `sr.stopReasonName = '维修原因'`）。需要处理潜在的大小写和前后空格问题。
*   **逻辑:** `UPPER(TRIM(sr.stopReasonName))` 等于拆分后的参数列表中的任何一个 `UPPER(TRIM(值))`。
*   **实现尝试 1 (失败):** 使用 `EXISTS` + `CONNECT BY REGEXP_SUBSTR`
    ```sql
    -- 使用 EXISTS (忽略大小写和空格) - 此版本在测试中未能按预期工作
    AND (p.stopReasonNames_param IS NULL OR EXISTS (
        SELECT 1
        FROM (
            SELECT UPPER(TRIM(REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL))) AS reason
            FROM dual
            CONNECT BY REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
        ) split_reasons
        WHERE UPPER(TRIM(sr.stopReasonName)) = split_reasons.reason
    ))
    ```
*   **问题:** 尽管逻辑上等同于硬编码 `OR`，但在实际测试中，当 `p.stopReasonNames_param` 非空时，此查询无法返回预期结果（而硬编码 `OR` 可以）。具体原因不明，可能是特定环境或 `EXISTS` 与 `CONNECT BY` 结合的某种边缘情况。
*   **实现尝试 2 (成功):** 使用 `IN` + `CONNECT BY REGEXP_SUBSTR`
    ```sql
    -- 使用 IN 操作符 (忽略大小写和空格) - 此版本经验证有效
    AND (p.stopReasonNames_param IS NULL OR
         UPPER(TRIM(sr.stopReasonName)) IN (
             SELECT UPPER(TRIM(REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL)))
             FROM dual
             CONNECT BY REGEXP_SUBSTR(p.stopReasonNames_param, '[^,]+', 1, LEVEL) IS NOT NULL
         )
    )
    ```
*   **结论:** 对于需要精确匹配列表项的场景，`IN` 结合 `CONNECT BY REGEXP_SUBSTR` 是一个有效且相对直观的解决方案。

---

## 3. `plNos_param`

*   **需求:** **包含**匹配逗号分隔列表中的**任何一个**前缀（例如，`'Y2,W2,W3'` 应该匹配 `INSTR(pl.plNo, 'Y2') > 0` 或 `INSTR(pl.plNo, 'W2') > 0` 或 `INSTR(pl.plNo, 'W3') > 0`）。
*   **逻辑:** `pl.plNo` 字符串包含拆分后的参数列表中的任何一个前缀。
*   **实现尝试 1 (理论可行，测试中遇阻):** 使用 `EXISTS` + `CONNECT BY REGEXP_SUBSTR` + `INSTR`
    ```sql
    -- 使用 EXISTS 和 INSTR - 逻辑正确，但用户测试中似乎未达预期
    AND (p.plNos_param IS NULL OR EXISTS (
        SELECT 1
        FROM (
            SELECT REGEXP_SUBSTR(p.plNos_param, '[^,]+', 1, LEVEL) AS pln
            FROM dual
            CONNECT BY REGEXP_SUBSTR(p.plNos_param, '[^,]+', 1, LEVEL) IS NOT NULL
        ) split_plnos
        WHERE INSTR(pl.plNo, split_plnos.pln) > 0
    ))
    ```
*   **问题:** 虽然逻辑上正确模拟了 `INSTR` 的 `OR` 条件，但在用户的一个测试场景中似乎未能按预期生效。标准的 `IN` 操作符不适用，因为它执行精确匹配而非包含匹配。
*   **实现尝试 2 (推荐):** 使用 `REGEXP_LIKE`
    ```sql
    -- 使用 REGEXP_LIKE - 更简洁且可能更健壮
    AND (p.plNos_param IS NULL OR REGEXP_LIKE(pl.plNo, REPLACE(p.plNos_param, ',', '|')))
    ```
*   **解释:** `REPLACE(p.plNos_param, ',', '|')` 将参数 `'Y2,W2,W3'` 转换为正则表达式模式 `'Y2|W2|W3'`。`REGEXP_LIKE(pl.plNo, 'Y2|W2|W3')` 会检查 `pl.plNo` 字符串是否包含 'Y2' **或** 'W2' **或** 'W3'。
*   **结论:** 对于需要“包含列表中任意一项”的逻辑，使用 `REGEXP_LIKE` 结合 `REPLACE` 将逗号转换成 `|` (正则表达式的 OR) 是一个更简洁、更直接且可能更优的解决方案。

---

**总结:**

在将动态 SQL 条件转换为静态 SQL 内的参数化逻辑时：
*   简单的精确匹配可以直接用 `OR` 实现。
*   精确匹配列表项时，`IN` + `CONNECT BY REGEXP_SUBSTR` 是一个可靠的选择。
*   包含匹配列表项时，`REGEXP_LIKE` + `REPLACE` 是一个简洁有效的选择。
*   `EXISTS` + `CONNECT BY REGEXP_SUBSTR` 的组合在某些情况下可能出现难以预料的问题，应谨慎使用或充分测试。
