<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yourpackage.YourMapperInterface"> <!-- 请替换为你的 Mapper 接口路径 -->

    <!-- 查询周度指标监控的极限值 -->
    <select id="queryMbjkJxzList" resultType="java.util.Map">
        SELECT DISTINCT
            pl_no AS "plNo",            -- 使用别名 "plNo"
            total_indicators AS "jxz"   -- 使用别名 "jxz"
        FROM
            erp_mo_indicator
        WHERE 1=1
          AND comp_id = 'sxjg'
          -- 恢复使用 LIKE 进行年份筛选 (更可靠)
          AND indicator_date LIKE SUBSTR(#{month, jdbcType=VARCHAR}, 1, 4) || '%'
          AND status >= 0
        ORDER BY
            pl_no
    </select>

</mapper>
